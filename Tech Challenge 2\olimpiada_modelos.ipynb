{"cells": [{"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from sklearn.model_selection import TimeSeriesSplit, cross_val_score\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier, VotingClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import accuracy_score, roc_auc_score\n", "import xgboost as xgb\n", "import lightgbm as lgb\n", "import matplotlib.pyplot as plt\n", "import time"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset processado: (3591, 10)\n"]}], "source": ["# Carregar e processar dados\n", "df = pd.read_csv('Dados Históricos - Ibovespa.csv', encoding='utf-8')\n", "df['Data'] = pd.to_datetime(df['Data'], format='%d.%m.%Y')\n", "df = df.sort_values('Data').reset_index(drop=True)\n", "\n", "# Converter volume\n", "def converter_volume(vol_str):\n", "    if pd.isna(vol_str): return np.nan\n", "    vol_str = str(vol_str).replace(',', '.')\n", "    if 'B' in vol_str: return float(vol_str.replace('B', '')) * 1e9\n", "    elif 'M' in vol_str: return float(vol_str.replace('M', '')) * 1e6\n", "    elif '<PERSON>' in vol_str: return float(vol_str.replace('K', '')) * 1e3\n", "    return float(vol_str)\n", "\n", "df['Volume'] = df['Vol.'].apply(converter_volume)\n", "df['Variacao'] = df['Var%'].str.replace('%', '').str.replace(',', '.').astype(float) / 100\n", "df['Target'] = (df['Variacao'].shift(-1) > 0).astype(int)\n", "df = df[:-1].copy()\n", "\n", "print(f\"Dataset processado: {df.shape}\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Features técnicas criadas: (3591, 42)\n"]}], "source": ["# Criar features t<PERSON><PERSON><PERSON>s\n", "df_features = df.copy()\n", "\n", "# M<PERSON><PERSON>s móveis\n", "for periodo in [5, 10, 20, 50]:\n", "    df_features[f'MA_{periodo}'] = df_features['Último'].rolling(window=periodo).mean()\n", "\n", "# Bandas de Bollinger\n", "df_features['BB_Middle'] = df_features['Último'].rolling(window=20).mean()\n", "bb_std = df_features['Último'].rolling(window=20).std()\n", "df_features['BB_Upper'] = df_features['BB_Middle'] + (bb_std * 2)\n", "df_features['BB_Lower'] = df_features['BB_Middle'] - (bb_std * 2)\n", "df_features['BB_Width'] = df_features['BB_Upper'] - df_features['BB_Lower']\n", "df_features['BB_Position'] = (df_features['Último'] - df_features['BB_Lower']) / df_features['BB_Width']\n", "\n", "# RSI\n", "def calculate_rsi(prices, window=14):\n", "    delta = prices.diff()\n", "    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()\n", "    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()\n", "    rs = gain / loss\n", "    return 100 - (100 / (1 + rs))\n", "\n", "df_features['RSI'] = calculate_rsi(df_features['Último'])\n", "\n", "# MACD\n", "ema_12 = df_features['Último'].ewm(span=12).mean()\n", "ema_26 = df_features['Último'].ewm(span=26).mean()\n", "df_features['MACD'] = ema_12 - ema_26\n", "df_features['Signal_Line'] = df_features['MACD'].ewm(span=9).mean()\n", "\n", "# ATR\n", "df_features['high_low'] = df_features['Máxi<PERSON>'] - df_features['<PERSON><PERSON><PERSON>']\n", "df_features['high_close_prev'] = abs(df_features['Máxima'] - df_features['Último'].shift(1))\n", "df_features['low_close_prev'] = abs(df_features['<PERSON>ín<PERSON>'] - df_features['Último'].shift(1))\n", "df_features['true_range'] = df_features[['high_low', 'high_close_prev', 'low_close_prev']].max(axis=1)\n", "\n", "for periodo in [5, 10, 20]:\n", "    df_features[f'atr_{periodo}'] = df_features['true_range'].rolling(window=periodo).mean()\n", "\n", "# Volatilidade\n", "df_features['returns'] = df_features['Último'].pct_change()\n", "for periodo in [5, 10, 20]:\n", "    df_features[f'volatility_{periodo}'] = df_features['returns'].rolling(window=periodo).std()\n", "\n", "# Features de preço\n", "df_features['Price_Range'] = df_features['Máxi<PERSON>'] - df_features['<PERSON>ín<PERSON>']\n", "df_features['Price_Position'] = (df_features['Último'] - df_features['Mínima']) / df_features['Price_Range']\n", "df_features['Gap'] = df_features['Abertura'] - df_features['Último'].shift(1)\n", "df_features['hl_close_ratio'] = (df_features['Máxima'] - df_features['<PERSON><PERSON><PERSON>']) / df_features['Último']\n", "\n", "# Features temporais\n", "df_features['day_of_week'] = df_features['Data'].dt.dayofweek\n", "df_features['month'] = df_features['Data'].dt.month\n", "df_features['quarter'] = df_features['Data'].dt.quarter\n", "df_features['is_month_start'] = (df_features['Data'].dt.day <= 5).astype(int)\n", "df_features['is_month_end'] = (df_features['Data'].dt.day >= 25).astype(int)\n", "\n", "print(f\"Features técnicas criadas: {df_features.shape}\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset A (Baseline): (3590, 40)\n"]}], "source": ["# Aplicar correção temporal\n", "features_to_shift = [\n", "    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>lt<PERSON>', 'Volume',\n", "    'MA_5', 'MA_10', 'MA_20', 'MA_50',\n", "    'BB_Upper', 'BB_Lower', 'BB_Width', 'BB_Position', 'BB_Middle',\n", "    'RSI', 'MACD', 'Signal_Line',\n", "    'atr_5', 'atr_10', 'atr_20',\n", "    'volatility_5', 'volatility_10', 'volatility_20',\n", "    'Price_Range', 'Price_Position', 'Gap', 'hl_close_ratio',\n", "    'true_range', 'high_close_prev', 'low_close_prev', 'high_low',\n", "    'returns', 'Variacao'\n", "]\n", "\n", "features_no_shift = [\n", "    'Data', 'day_of_week', 'month', 'quarter',\n", "    'is_month_start', 'is_month_end', 'Target'\n", "]\n", "\n", "# Dataset A (Baseline) - sem candlestick\n", "dataset_a = pd.DataFrame()\n", "for feature in features_no_shift:\n", "    if feature in df_features.columns:\n", "        dataset_a[feature] = df_features[feature]\n", "\n", "for feature in features_to_shift:\n", "    if feature in df_features.columns:\n", "        dataset_a[f'{feature}_shifted'] = df_features[feature].shift(1)\n", "\n", "dataset_a = dataset_a.iloc[1:].reset_index(drop=True)\n", "\n", "print(f\"Dataset A (Baseline): {dataset_a.shape}\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset B (Enriquecido): (3590, 49)\n", "Diferença: +9 features\n"]}], "source": ["# Criar features de candlestick\n", "dataset_b = dataset_a.copy()\n", "\n", "# Usar colunas OHLC shifted\n", "ohlc_cols = ['<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted']\n", "if all(col in dataset_b.columns for col in ohlc_cols):\n", "    ohlc_temp = dataset_b[ohlc_cols].copy()\n", "    ohlc_temp.columns = ['Open', 'High', 'Low', 'Close']\n", "    \n", "    # Padrões de candlestick\n", "    def detect_doji(df, threshold=0.1):\n", "        body_size = abs(df['Close'] - df['Open'])\n", "        total_range = df['High'] - df['Low']\n", "        return (body_size / total_range < threshold).astype(int)\n", "    \n", "    def detect_hammer(df):\n", "        body_size = abs(df['Close'] - df['Open'])\n", "        lower_shadow = df[['Open', 'Close']].min(axis=1) - df['Low']\n", "        upper_shadow = df['High'] - df[['Open', 'Close']].max(axis=1)\n", "        return ((lower_shadow > 2 * body_size) & (upper_shadow < body_size)).astype(int)\n", "    \n", "    def detect_shooting_star(df):\n", "        body_size = abs(df['Close'] - df['Open'])\n", "        lower_shadow = df[['Open', 'Close']].min(axis=1) - df['Low']\n", "        upper_shadow = df['High'] - df[['Open', 'Close']].max(axis=1)\n", "        return ((upper_shadow > 2 * body_size) & (lower_shadow < body_size)).astype(int)\n", "    \n", "    def detect_engulfing_bullish(df):\n", "        current_bullish = df['Close'] > df['Open']\n", "        prev_bearish = (df['Close'].shift(1) < df['Open'].shift(1))\n", "        current_open_below_prev_close = df['Open'] < df['Close'].shift(1)\n", "        current_close_above_prev_open = df['Close'] > df['Open'].shift(1)\n", "        return (current_bullish & prev_bearish & current_open_below_prev_close & current_close_above_prev_open).astype(int)\n", "    \n", "    # Aplicar pad<PERSON><PERSON><PERSON>\n", "    dataset_b['doji_prev'] = detect_doji(ohlc_temp)\n", "    dataset_b['hammer_prev'] = detect_hammer(ohlc_temp)\n", "    dataset_b['shooting_star_prev'] = detect_shooting_star(ohlc_temp)\n", "    dataset_b['engulfing_bullish_prev'] = detect_engulfing_bullish(ohlc_temp)\n", "    dataset_b['bullish_candle_prev'] = (ohlc_temp['Close'] > ohlc_temp['Open']).astype(int)\n", "    dataset_b['bearish_candle_prev'] = (ohlc_temp['Close'] < ohlc_temp['Open']).astype(int)\n", "    dataset_b['body_size_prev'] = abs(ohlc_temp['Close'] - ohlc_temp['Open'])\n", "    dataset_b['upper_shadow_prev'] = ohlc_temp['High'] - ohlc_temp[['Open', 'Close']].max(axis=1)\n", "    dataset_b['lower_shadow_prev'] = ohlc_temp[['Open', 'Close']].min(axis=1) - ohlc_temp['Low']\n", "\n", "print(f\"Dataset B (Enriquecido): {dataset_b.shape}\")\n", "print(f\"Diferença: +{dataset_b.shape[1] - dataset_a.shape[1]} features\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset A - Train: (2872, 38), Test: (718, 38)\n", "Dataset B - Train: (2872, 47), Test: (718, 47)\n", "NaN removidos\n"]}], "source": ["# Preparar splits temporais\n", "def create_temporal_split(df, test_size=0.2):\n", "    df_sorted = df.sort_values('Data').reset_index(drop=True) if 'Data' in df.columns else df.copy()\n", "    split_idx = int(len(df_sorted) * (1 - test_size))\n", "    \n", "    train_df = df_sorted.iloc[:split_idx]\n", "    test_df = df_sorted.iloc[split_idx:]\n", "    \n", "    feature_cols = [col for col in df_sorted.columns if col not in ['Data', 'Target']]\n", "    \n", "    X_train = train_df[feature_cols]\n", "    X_test = test_df[feature_cols]\n", "    y_train = train_df['Target']\n", "    y_test = test_df['Target']\n", "    \n", "    return X_train, X_test, y_train, y_test\n", "\n", "X_train_a, X_test_a, y_train_a, y_test_a = create_temporal_split(dataset_a)\n", "X_train_b, X_test_b, y_train_b, y_test_b = create_temporal_split(dataset_b)\n", "\n", "# Remover NaN\n", "X_train_a = X_train_a.fillna(method='ffill').fillna(0)\n", "X_test_a = X_test_a.fillna(method='ffill').fillna(0)\n", "X_train_b = X_train_b.fillna(method='ffill').fillna(0)\n", "X_test_b = X_test_b.fillna(method='ffill').fillna(0)\n", "\n", "print(f\"Dataset A - Train: {X_train_a.shape}, Test: {X_test_a.shape}\")\n", "print(f\"Dataset B - Train: {X_train_b.shape}, Test: {X_test_b.shape}\")\n", "print(f\"NaN removidos\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Modelos preparados: ['Logistic Regression', 'Random Forest', 'XGBoost', 'LightGBM', 'SVM']\n"]}], "source": ["# Definir modelos\n", "models = {\n", "    'Logistic Regression': Pipeline([\n", "        ('scaler', StandardScaler()),\n", "        ('logreg', LogisticRegression(C=0.1, solver='liblinear', random_state=42))\n", "    ]),\n", "    \n", "    'Random Forest': RandomForestClassifier(\n", "        n_estimators=100, max_depth=10, min_samples_split=5,\n", "        min_samples_leaf=2, random_state=42, n_jobs=-1\n", "    ),\n", "    \n", "    'XGBoost': xgb.XGBClassifier(\n", "        n_estimators=100, max_depth=6, learning_rate=0.1,\n", "        subsample=0.8, colsample_bytree=0.8, random_state=42, eval_metric='logloss'\n", "    ),\n", "    \n", "    'LightGBM': lgb.LGBMClassifier(\n", "        n_estimators=100, max_depth=6, learning_rate=0.1,\n", "        subsample=0.8, colsample_bytree=0.8, random_state=42, verbose=-1\n", "    ),\n", "    \n", "    'SVM': Pipeline([\n", "        ('scaler', StandardScaler()),\n", "        ('svm', SVC(kernel='rbf', probability=True, random_state=42, C=1.0))\n", "    ])\n", "}\n", "\n", "print(f\"Modelos preparados: {list(models.keys())}\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Função de avaliação preparada\n"]}], "source": ["# Função de avaliação\n", "def evaluate_model(model, X_train, X_test, y_train, y_test, model_name, dataset_name):\n", "    start_time = time.time()\n", "    \n", "    model.fit(X_train, y_train)\n", "    y_pred = model.predict(X_test)\n", "    y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None\n", "    \n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    auc_score = roc_auc_score(y_test, y_pred_proba) if y_pred_proba is not None else None\n", "    training_time = time.time() - start_time\n", "    \n", "    tscv = TimeSeriesSplit(n_splits=5)\n", "    cv_scores = cross_val_score(model, X_train, y_train, cv=tscv, scoring='accuracy')\n", "    \n", "    return {\n", "        'model_name': model_name,\n", "        'dataset': dataset_name,\n", "        'accuracy': accuracy,\n", "        'auc_score': auc_score,\n", "        'cv_mean': cv_scores.mean(),\n", "        'cv_std': cv_scores.std(),\n", "        'training_time': training_time,\n", "        'model': model\n", "    }\n", "\n", "print(\"Função de avaliação preparada\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "<PERSON><PERSON>o Lo<PERSON> Regression...\n", "  Dataset A: Acc=0.4875, AUC=0.5031\n", "  Dataset B: Acc=0.4861, AUC=0.5094\n", "  Diferença: -0.0014\n", "\n", "Testando Random Forest...\n", "  Dataset A: Acc=0.5334, AUC=0.5379\n", "  Dataset B: Acc=0.5195, AUC=0.5337\n", "  Diferença: -0.0139\n", "\n", "Testando XGBoost...\n", "  Dataset A: Acc=0.5306, AUC=0.5392\n", "  Dataset B: Acc=0.5404, AUC=0.5456\n", "  Diferença: +0.0097\n", "\n", "Testando LightGBM...\n", "  Dataset A: Acc=0.4986, AUC=0.4979\n", "  Dataset B: Acc=0.5237, AUC=0.5243\n", "  Diferença: +0.0251\n", "\n", "Testando SVM...\n", "  Dataset A: Acc=0.5460, AUC=0.4560\n", "  Dataset B: Acc=0.5376, AUC=0.4540\n", "  Diferença: -0.0084\n", "\n", "Resultados coletados: 10\n"]}], "source": ["# Executar olimpíada\n", "results = []\n", "\n", "for model_name, model in models.items():\n", "    print(f\"\\nTestando {model_name}...\")\n", "    \n", "    # Dataset A\n", "    try:\n", "        result_a = evaluate_model(model, X_train_a, X_test_a, y_train_a, y_test_a, \n", "                                model_name, \"Dataset A\")\n", "        results.append(result_a)\n", "        auc_str_a = f'{result_a[\"auc_score\"]:.4f}' if result_a['auc_score'] else 'N/A'\n", "        print(f'  Dataset A: Acc={result_a[\"accuracy\"]:.4f}, AUC={auc_str_a}')\n", "\n", "    except Exception as e:\n", "        print(f\"  Dataset A: Error - {e}\")\n", "    \n", "    # Dataset B\n", "    try:\n", "        result_b = evaluate_model(model, X_train_b, X_test_b, y_train_b, y_test_b, \n", "                                model_name, \"Dataset B\")\n", "        results.append(result_b)\n", "        auc_str_b = f'{result_b[\"auc_score\"]:.4f}' if result_b['auc_score'] else 'N/A'\n", "        print(f'  Dataset B: Acc={result_b[\"accuracy\"]:.4f}, AUC={auc_str_b}')\n", "\n", "        \n", "        if len(results) >= 2:\n", "            diff = result_b['accuracy'] - result_a['accuracy']\n", "            print(f\"  Diferença: {diff:+.4f}\")\n", "    except Exception as e:\n", "        print(f\"  Dataset B: Error - {e}\")\n", "\n", "print(f\"\\nResultados coletados: {len(results)}\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tabela de Resultados:\n", "Modelo               Dataset      Accuracy   AUC      CV Mean    Tempo   \n", "---------------------------------------------------------------------------\n", "Logistic Regression  Dataset A    0.4875     0.5031   0.5013     0.06    \n", "Logistic Regression  Dataset B    0.4861     0.5094   0.4971     0.04    \n", "Random Forest        Dataset A    0.5334     0.5379   0.4950     0.42    \n", "Random Forest        Dataset B    0.5195     0.5337   0.4858     0.31    \n", "XGBoost              Dataset A    0.5306     0.5392   0.5067     0.55    \n", "XGBoost              Dataset B    0.5404     0.5456   0.4962     0.41    \n", "LightGBM             Dataset A    0.4986     0.4979   0.5038     1.57    \n", "LightGBM             Dataset B    0.5237     0.5243   0.5113     0.11    \n", "SVM                  Dataset A    0.5460     0.4560   0.4874     2.82    \n", "SVM                  Dataset B    0.5376     0.4540   0.4854     3.09    \n", "\n", "Ranking Dataset A:\n", "  1. SVM: 0.5460\n", "  2. <PERSON> Forest: 0.5334\n", "  3. XGBoost: 0.5306\n", "  4. LightGBM: 0.4986\n", "  5. Logistic Regression: 0.4875\n", "\n", "Ranking Dataset B:\n", "  1. XGBoost: 0.5404\n", "  2. SVM: 0.5376\n", "  3. LightGBM: 0.5237\n", "  4. <PERSON> Forest: 0.5195\n", "  5. Logistic Regression: 0.4861\n", "\n", "Impacto das Features de Candlestick:\n", "  Logistic Regression: 0.4875 -> 0.4861 (-0.0014)\n", "  Random Forest: 0.5334 -> 0.5195 (-0.0139)\n", "  XGBoost: 0.5306 -> 0.5404 (+0.0097)\n", "  LightGBM: 0.4986 -> 0.5237 (+0.0251)\n", "  SVM: 0.5460 -> 0.5376 (-0.0084)\n", "\n", "Resumo do Impacto:\n", "  Mel<PERSON>ia média: +0.0022\n", "  Modelos que melhoraram: 2/5\n", "  Conclusão: Features de candlestick agregam valor marginal\n", "\n", "Campeão Geral:\n", "  Modelo: SVM\n", "  Dataset: Dataset A\n", "  Accuracy: 0.5460\n", "  AUC: 0.4560\n"]}], "source": ["# <PERSON><PERSON><PERSON><PERSON> dos resultados\n", "if results:\n", "    results_df = pd.DataFrame(results)\n", "    \n", "    print(\"\\nTabela de Resultados:\")\n", "    print(f\"{'Modelo':<20} {'Dataset':<12} {'Accuracy':<10} {'AUC':<8} {'CV Mean':<10} {'Tempo':<8}\")\n", "    print(\"-\" * 75)\n", "    \n", "    for _, row in results_df.iterrows():\n", "        auc_str = f\"{row['auc_score']:.4f}\" if row['auc_score'] is not None else \"N/A\"\n", "        print(f\"{row['model_name']:<20} {row['dataset']:<12} {row['accuracy']:<10.4f} {auc_str:<8} {row['cv_mean']:<10.4f} {row['training_time']:<8.2f}\")\n", "    \n", "    # Ranking por dataset\n", "    print(\"\\nRanking Dataset A:\")\n", "    dataset_a_results = results_df[results_df['dataset'] == 'Dataset A'].sort_values('accuracy', ascending=False)\n", "    for i, (_, row) in enumerate(dataset_a_results.iterrows(), 1):\n", "        print(f\"  {i}. {row['model_name']}: {row['accuracy']:.4f}\")\n", "    \n", "    print(\"\\nRanking Dataset B:\")\n", "    dataset_b_results = results_df[results_df['dataset'] == 'Dataset B'].sort_values('accuracy', ascending=False)\n", "    for i, (_, row) in enumerate(dataset_b_results.iterrows(), 1):\n", "        print(f\"  {i}. {row['model_name']}: {row['accuracy']:.4f}\")\n", "    \n", "    # Impact<PERSON> das features de candlestick\n", "    print(\"\\nImpacto das Features de Candlestick:\")\n", "    candlestick_impact = []\n", "    \n", "    for model_name in results_df['model_name'].unique():\n", "        model_results = results_df[results_df['model_name'] == model_name]\n", "        if len(model_results) == 2:\n", "            baseline = model_results[model_results['dataset'] == 'Dataset A'].iloc[0]\n", "            enriched = model_results[model_results['dataset'] == 'Dataset B'].iloc[0]\n", "            \n", "            improvement = enriched['accuracy'] - baseline['accuracy']\n", "            candlestick_impact.append(improvement)\n", "            \n", "            print(f\"  {model_name}: {baseline['accuracy']:.4f} -> {enriched['accuracy']:.4f} ({improvement:+.4f})\")\n", "    \n", "    if candlestick_impact:\n", "        avg_improvement = np.mean(candlestick_impact)\n", "        positive_count = sum(1 for x in candlestick_impact if x > 0)\n", "        \n", "        print(f\"\\nResumo do Impacto:\")\n", "        print(f\"  Melhoria média: {avg_improvement:+.4f}\")\n", "        print(f\"  Modelos que melhoraram: {positive_count}/{len(candlestick_impact)}\")\n", "        \n", "        if avg_improvement > 0.01:\n", "            print(f\"  Conclusão: Features de candlestick agregam valor significativo\")\n", "        elif avg_improvement > 0:\n", "            print(f\"  Conclusão: Features de candlestick agregam valor marginal\")\n", "        else:\n", "            print(f\"  Conclusão: Features de candlestick não agregam valor\")\n", "    \n", "    # Campeão geral\n", "    best_result = results_df.loc[results_df['accuracy'].idxmax()]\n", "    print(f\"\\nCampeão Geral:\")\n", "    print(f\"  Modelo: {best_result['model_name']}\")\n", "    print(f\"  Dataset: {best_result['dataset']}\")\n", "    print(f\"  Accuracy: {best_result['accuracy']:.4f}\")\n", "    print(f\"  AUC: {best_result['auc_score']:.4f}\" if best_result['auc_score'] else \"  AUC: N/A\")\n", "\n", "else:\n", "    print(\"Nenhum resultado coletado\")"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 3 para ensemble: ['XGBoost', 'SVM', 'LightGBM']\n", "\n", "Ensemble Result:\n", "  Accuracy: 0.5362\n", "  AUC: 0.5380\n", "  vs Best Individual: -0.0042\n", "\n", "Olimpíada concluída\n"]}], "source": ["# Ensemble dos melhores\n", "if len(results) >= 6:  # Pelo menos 3 modelos testados em ambos datasets\n", "    dataset_b_results = [r for r in results if r['dataset'] == 'Dataset B']\n", "    \n", "    if len(dataset_b_results) >= 3:\n", "        dataset_b_results.sort(key=lambda x: x['accuracy'], reverse=True)\n", "        top_3 = dataset_b_results[:3]\n", "        \n", "        print(f\"\\nTop 3 para ensemble: {[r['model_name'] for r in top_3]}\")\n", "        \n", "        ensemble_estimators = []\n", "        for i, result in enumerate(top_3):\n", "            name = f\"model_{i+1}\"\n", "            ensemble_estimators.append((name, result['model']))\n", "        \n", "        voting_classifier = VotingClassifier(estimators=ensemble_estimators, voting='soft')\n", "        \n", "        try:\n", "            ensemble_result = evaluate_model(\n", "                voting_classifier, X_train_b, X_test_b, y_train_b, y_test_b,\n", "                \"Voting Classifier\", \"Dataset B\"\n", "            )\n", "            \n", "            print(f\"\\nEnsemble Result:\")\n", "            print(f\"  Accuracy: {ensemble_result['accuracy']:.4f}\")\n", "            print(f\"  AUC: {ensemble_result['auc_score']:.4f}\")\n", "            \n", "            best_individual = top_3[0]\n", "            improvement = ensemble_result['accuracy'] - best_individual['accuracy']\n", "            print(f\"  vs Best Individual: {improvement:+.4f}\")\n", "            \n", "            results.append(ensemble_result)\n", "            \n", "        except Exception as e:\n", "            print(f\"Ensemble error: {e}\")\n", "\n", "print(\"\\nOlimpíada concluída\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 1200x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Análise visual concluída\n"]}], "source": ["# Visualização simples\n", "if results:\n", "    results_df = pd.DataFrame(results)\n", "    \n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # Gráfico de accuracy por modelo e dataset\n", "    plt.subplot(2, 2, 1)\n", "    models_list = results_df['model_name'].unique()\n", "    datasets_list = results_df['dataset'].unique()\n", "    \n", "    x = np.arange(len(models_list))\n", "    width = 0.35\n", "    \n", "    for i, dataset in enumerate(datasets_list):\n", "        dataset_data = results_df[results_df['dataset'] == dataset]\n", "        accuracies = []\n", "        \n", "        for model in models_list:\n", "            model_data = dataset_data[dataset_data['model_name'] == model]\n", "            if len(model_data) > 0:\n", "                accuracies.append(model_data.iloc[0]['accuracy'])\n", "            else:\n", "                accuracies.append(0)\n", "        \n", "        plt.bar(x + i*width, accuracies, width, label=dataset, alpha=0.8)\n", "    \n", "    plt.xlabel('Modelos')\n", "    plt.ylabel('Accuracy')\n", "    plt.title('Accuracy por Modelo e Dataset')\n", "    plt.xticks(x + width/2, models_list, rotation=45, ha='right')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Tempo vs Accuracy\n", "    plt.subplot(2, 2, 2)\n", "    plt.scatter([r['training_time'] for r in results], \n", "               [r['accuracy'] for r in results], alpha=0.7)\n", "    plt.xlabel('Tempo de Treinamento (s)')\n", "    plt.ylabel('Accuracy')\n", "    plt.title('Accuracy vs Tempo')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Distribuição de AUC\n", "    plt.subplot(2, 2, 3)\n", "    auc_scores = [r['auc_score'] for r in results if r['auc_score'] is not None]\n", "    if auc_scores:\n", "        plt.hist(auc_scores, bins=10, alpha=0.7, edgecolor='black')\n", "        plt.xlabel('AUC Score')\n", "        plt.ylabel('Frequência')\n", "        plt.title('Distribuição AUC')\n", "        plt.grid(True, alpha=0.3)\n", "    \n", "    # Impacto candlestick\n", "    plt.subplot(2, 2, 4)\n", "    if 'candlestick_impact' in locals() and candlestick_impact:\n", "        models_impact = [results_df[results_df['model_name'] == model]['model_name'].iloc[0] \n", "                        for model in results_df['model_name'].unique() \n", "                        if len(results_df[results_df['model_name'] == model]) == 2]\n", "        \n", "        if len(models_impact) == len(candlestick_impact):\n", "            colors = ['green' if x > 0 else 'red' for x in candlestick_impact]\n", "            plt.bar(models_impact, candlestick_impact, color=colors, alpha=0.7)\n", "            plt.xlabel('Modelos')\n", "            plt.ylabel('Melhoria Accuracy')\n", "            plt.title('Impacto Features Candlestick')\n", "            plt.xticks(rotation=45, ha='right')\n", "            plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "            plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "print(\"Análise visual concluída\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}