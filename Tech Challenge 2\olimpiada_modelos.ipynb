{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from sklearn.model_selection import TimeSeriesSplit, cross_val_score\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier, VotingClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import accuracy_score, roc_auc_score\n", "import xgboost as xgb\n", "import lightgbm as lgb\n", "import matplotlib.pyplot as plt\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 🚨 PIPELINE EXPLÍCITO: RASTREAMENTO COMPLETO DE FEATURES\n", "\n", "## Estratégia de Correção:\n", "1. **Pipeline Explícito**: Cada dataframe tem nome único e descritivo\n", "2. **Auditoria de Penhascos**: Identificar exatamente onde features s<PERSON> perdidas\n", "3. **Checkpoints com .info()**: Inspeção detalhada em cada etapa\n", "4. **Preservação Rigorosa**: Garantir que nenhuma feature seja perdida\n", "\n", "## Nomenclatura do Pipeline:\n", "- `df_step01_raw`: <PERSON><PERSON> br<PERSON>s\n", "- `df_step02_processed`: Dados processados básicos\n", "- `df_step03_features`: Com features técnicas criadas\n", "- `df_step04_selected`: Após seleção de features\n", "- `df_step05_temporal`: Após correção temporal\n", "- `df_step06_candlestick`: Com padrões de candlestick\n", "- `df_step07_final`: Dataset final para modelagem"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Função de auditoria rigorosa criada\n", "📋 Pronta para rastrear cada etapa do pipeline\n"]}], "source": ["# FUNÇÃO DE AUDITORIA RIGOROSA\n", "def audit_pipeline_step(df, step_name, expected_min_cols=None, previous_df=None):\n", "    \"\"\"\n", "    Auditoria rigorosa de cada etapa do pipeline\n", "    Identifica exatamente onde features s<PERSON> perdidas\n", "    \"\"\"\n", "    print(f\"\\n{'='*60}\")\n", "    print(f\"🔍 AUDITORIA: {step_name.upper()}\")\n", "    print(f\"{'='*60}\")\n", "    \n", "    # Informações básicas\n", "    print(f\"📊 INFORMAÇÕES BÁSICAS:\")\n", "    print(f\"   Shape: {df.shape}\")\n", "    print(f\"   Memória: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    \n", "    # Usar .info() para inspeção detalhada\n", "    print(f\"\\n📋 DETALHES DAS COLUNAS (.info()):\")\n", "    df.info()\n", "    \n", "    # Categorização de features\n", "    ohlc_cols = [col for col in df.columns if col in ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Último', 'Volume']]\n", "    temporal_cols = [col for col in df.columns if any(x in col.lower() for x in ['data', 'day', 'month', 'quarter', 'year'])]\n", "    technical_cols = [col for col in df.columns if any(x in col.lower() for x in ['ma_', 'bb_', 'rsi', 'atr', 'volatility', 'macd'])]\n", "    shifted_cols = [col for col in df.columns if col.endswith('_shifted')]\n", "    candlestick_cols = [col for col in df.columns if any(x in col.lower() for x in ['doji', 'hammer', 'engulf', 'marubozu', 'shooting', '_prev'])]\n", "    target_cols = [col for col in df.columns if 'target' in col.lower()]\n", "    other_cols = [col for col in df.columns if col not in ohlc_cols + temporal_cols + technical_cols + shifted_cols + candlestick_cols + target_cols]\n", "    \n", "    print(f\"\\n📂 CATEGORIZAÇÃO DE FEATURES:\")\n", "    print(f\"   🏢 OHLC/Volume ({len(ohlc_cols)}): {ohlc_cols}\")\n", "    print(f\"   📅 Temporais ({len(temporal_cols)}): {temporal_cols}\")\n", "    print(f\"   📈 Técnicas ({len(technical_cols)}): {technical_cols[:5]}{'...' if len(technical_cols) > 5 else ''}\")\n", "    print(f\"   ⏰ Shifted ({len(shifted_cols)}): {shifted_cols[:5]}{'...' if len(shifted_cols) > 5 else ''}\")\n", "    print(f\"   🕯️ Candlestick ({len(candlestick_cols)}): {candlestick_cols[:5]}{'...' if len(candlestick_cols) > 5 else ''}\")\n", "    print(f\"   🎯 Target ({len(target_cols)}): {target_cols}\")\n", "    print(f\"   ❓ Outras ({len(other_cols)}): {other_cols[:5]}{'...' if len(other_cols) > 5 else ''}\")\n", "    \n", "    # Verificar se há perda de features\n", "    if previous_df is not None:\n", "        print(f\"\\n🚨 ANÁLISE DE MUDANÇAS:\")\n", "        prev_cols = set(previous_df.columns)\n", "        curr_cols = set(df.columns)\n", "        \n", "        lost_features = prev_cols - curr_cols\n", "        gained_features = curr_cols - prev_cols\n", "        \n", "        print(f\"   📉 Features PERDIDAS ({len(lost_features)}):\")\n", "        if lost_features:\n", "            for feature in sorted(lost_features):\n", "                print(f\"      ❌ {feature}\")\n", "        else:\n", "            print(f\"      ✅ Nenhuma feature perdida\")\n", "        \n", "        print(f\"   📈 Features GANHAS ({len(gained_features)}):\")\n", "        if gained_features:\n", "            for feature in sorted(gained_features):\n", "                print(f\"      ✅ {feature}\")\n", "        else:\n", "            print(f\"      ➖ Nenhuma feature ganha\")\n", "        \n", "        net_change = len(curr_cols) - len(prev_cols)\n", "        print(f\"   📊 Mudança líquida: {net_change:+d} features\")\n", "        \n", "        # ALERTA para perdas significativas\n", "        if len(lost_features) > 5:\n", "            print(f\"\\n🚨 ALERTA: PERDA SIGNIFICATIVA DE FEATURES!\")\n", "            print(f\"   {len(lost_features)} features foram perdidas nesta etapa\")\n", "            print(f\"   Isso pode indicar um 'PENHASCO' no pipeline\")\n", "    \n", "    # Verificar expectativas\n", "    if expected_min_cols and len(df.columns) < expected_min_cols:\n", "        print(f\"\\n⚠️ AVISO: <PERSON>os colunas que esperado\")\n", "        print(f\"   Esperado: >= {expected_min_cols}\")\n", "        print(f\"   Atual: {len(df.columns)}\")\n", "    \n", "    # Verificar qualidade dos dados\n", "    print(f\"\\n🔍 QUALIDADE DOS DADOS:\")\n", "    null_counts = df.isnull().sum()\n", "    cols_with_nulls = null_counts[null_counts > 0]\n", "    print(f\"   Colunas com valores ausentes: {len(cols_with_nulls)}\")\n", "    if len(cols_with_nulls) > 0:\n", "        print(f\"   Top 5 com mais NaNs:\")\n", "        for col, count in cols_with_nulls.head().items():\n", "            print(f\"      {col}: {count} ({count/len(df):.1%})\")\n", "    \n", "    print(f\"\\n✅ Auditoria de {step_name} concluída\")\n", "    print(f\"{'='*60}\")\n", "    \n", "    return {\n", "        'step_name': step_name,\n", "        'shape': df.shape,\n", "        'columns': list(df.columns),\n", "        'ohlc_count': len(ohlc_cols),\n", "        'technical_count': len(technical_cols),\n", "        'shifted_count': len(shifted_cols),\n", "        'candlestick_count': len(candlestick_cols)\n", "    }\n", "\n", "print(\"✅ Função de auditoria rigorosa criada\")\n", "print(\"📋 Pronta para rastrear cada etapa do pipeline\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## STEP 01: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> DOS DADOS BRUTOS"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 INICIANDO PIPELINE EXPLÍCITO\n", "📂 STEP 01: Carregando dados brutos...\n", "\n", "============================================================\n", "🔍 AUDITORIA: STEP 01 - DADOS BRUTOS\n", "============================================================\n", "📊 INFORMAÇÕES BÁSICAS:\n", "   Shape: (3592, 7)\n", "   Memória: 0.68 MB\n", "\n", "📋 DETALHES DAS COLUNAS (.info()):\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3592 entries, 0 to 3591\n", "Data columns (total 7 columns):\n", " #   Column    Non-Null Count  Dtype  \n", "---  ------    --------------  -----  \n", " 0   Data      3592 non-null   object \n", " 1   Último    3592 non-null   float64\n", " 2   Abertura  3592 non-null   float64\n", " 3   Máxima    3592 non-null   float64\n", " 4   Mínima    3592 non-null   float64\n", " 5   Vol.      3591 non-null   object \n", " 6   Var%      3592 non-null   object \n", "dtypes: float64(4), object(3)\n", "memory usage: 196.6+ KB\n", "\n", "📂 CATEGORIZAÇÃO DE FEATURES:\n", "   🏢 OHLC/Volume (4): ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>']\n", "   📅 Temporais (1): ['Data']\n", "   📈 Técnicas (0): []\n", "   ⏰ Shifted (0): []\n", "   🕯️ Candlestick (0): []\n", "   🎯 Target (0): []\n", "   ❓ Outras (2): ['Vol.', 'Var%']\n", "\n", "🔍 QUALIDADE DOS DADOS:\n", "   Colunas com valores ausentes: 1\n", "   Top 5 com mais NaNs:\n", "      Vol.: 1 (0.0%)\n", "\n", "✅ Auditoria de STEP 01 - Dados Brutos concluída\n", "============================================================\n", "\n", "📋 RESUMO STEP 01:\n", "   ✅ <PERSON><PERSON> carregados: (3592, 7)\n", "   📊 Colunas originais: ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Vol.', 'Var%']\n"]}], "source": ["# STEP 01: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> DOS DADOS BRUTOS\n", "print(\"🚀 INICIANDO PIPELINE EXPLÍCITO\")\n", "print(\"📂 STEP 01: Carregando dados brutos...\")\n", "\n", "# <PERSON><PERSON>ar dados brutos\n", "df_step01_raw = pd.read_csv('Dados Históricos - Ibovespa.csv', encoding='utf-8')\n", "\n", "# Auditoria do carregamento\n", "audit_step01 = audit_pipeline_step(df_step01_raw, \"STEP 01 - <PERSON><PERSON> Brutos\")\n", "\n", "print(f\"\\n📋 RESUMO STEP 01:\")\n", "print(f\"   ✅ <PERSON><PERSON> carregados: {df_step01_raw.shape}\")\n", "print(f\"   📊 Colunas originais: {list(df_step01_raw.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## STEP 02: PROCESSAMENTO BÁSICO"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📂 STEP 02: Processamento básico...\n", "\n", "============================================================\n", "🔍 AUDITORIA: STEP 02 - PROCESSAMENTO BÁSICO\n", "============================================================\n", "📊 INFORMAÇÕES BÁSICAS:\n", "   Shape: (3591, 10)\n", "   Memória: 0.59 MB\n", "\n", "📋 DETALHES DAS COLUNAS (.info()):\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3591 entries, 0 to 3590\n", "Data columns (total 10 columns):\n", " #   Column    Non-Null Count  Dtype         \n", "---  ------    --------------  -----         \n", " 0   Data      3591 non-null   datetime64[ns]\n", " 1   Último    3591 non-null   float64       \n", " 2   Abertura  3591 non-null   float64       \n", " 3   Máxima    3591 non-null   float64       \n", " 4   Mínima    3591 non-null   float64       \n", " 5   Vol.      3591 non-null   object        \n", " 6   Var%      3591 non-null   object        \n", " 7   Volume    3591 non-null   float64       \n", " 8   Variacao  3591 non-null   float64       \n", " 9   Target    3591 non-null   int64         \n", "dtypes: datetime64[ns](1), float64(6), int64(1), object(2)\n", "memory usage: 280.7+ KB\n", "\n", "📂 CATEGORIZAÇÃO DE FEATURES:\n", "   🏢 OHLC/Volume (5): ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Volume']\n", "   📅 Temporais (1): ['Data']\n", "   📈 Técnicas (0): []\n", "   ⏰ Shifted (0): []\n", "   🕯️ Candlestick (0): []\n", "   🎯 Target (1): ['Target']\n", "   ❓ Outras (3): ['Vol.', 'Var%', 'Variacao']\n", "\n", "🚨 ANÁLISE DE MUDANÇAS:\n", "   📉 Features PERDIDAS (0):\n", "      ✅ Nenhuma feature perdida\n", "   📈 Features GANHAS (3):\n", "      ✅ Target\n", "      ✅ Variacao\n", "      ✅ Volume\n", "   📊 Mudança líquida: +3 features\n", "\n", "🔍 QUALIDADE DOS DADOS:\n", "   Colunas com valores ausentes: 0\n", "\n", "✅ Auditoria de STEP 02 - Processamento Básico concluída\n", "============================================================\n", "\n", "📋 RESUMO STEP 02:\n", "   ✅ Target criado corretamente\n", "   📊 Shape após processamento: (3591, 10)\n", "   🎯 Distribuição do target:\n", "Target\n", "1    0.510721\n", "0    0.489279\n", "Name: proportion, dtype: float64\n"]}], "source": ["# STEP 02: PROCESSAMENTO BÁSICO\n", "print(\"\\n📂 STEP 02: Processamento básico...\")\n", "\n", "# Criar cópia para processamento\n", "df_step02_processed = df_step01_raw.copy()\n", "\n", "# Processamento de data\n", "df_step02_processed['Data'] = pd.to_datetime(df_step02_processed['Data'], format='%d.%m.%Y')\n", "df_step02_processed = df_step02_processed.sort_values('Data').reset_index(drop=True)\n", "\n", "# Tratar valores ausentes\n", "df_step02_processed['Vol.'] = df_step02_processed['Vol.'].fillna(method='ffill')\n", "\n", "# Converter Volume para numérico\n", "def converter_volume(vol_str):\n", "    if pd.isna(vol_str): return np.nan\n", "    vol_str = str(vol_str).replace(',', '.')\n", "    if 'B' in vol_str: return float(vol_str.replace('B', '')) * 1e9\n", "    elif 'M' in vol_str: return float(vol_str.replace('M', '')) * 1e6\n", "    elif '<PERSON>' in vol_str: return float(vol_str.replace('K', '')) * 1e3\n", "    return float(vol_str)\n", "\n", "df_step02_processed['Volume'] = df_step02_processed['Vol.'].apply(converter_volume)\n", "df_step02_processed['Variacao'] = df_step02_processed['Var%'].str.replace('%', '').str.replace(',', '.').astype(float) / 100\n", "\n", "# Criar target\n", "df_step02_processed['Target'] = (df_step02_processed['Variacao'].shift(-1) > 0).astype(int)\n", "df_step02_processed = df_step02_processed[:-1].copy()  # Remove última linha\n", "\n", "# Auditoria do processamento\n", "audit_step02 = audit_pipeline_step(\n", "    df_step02_processed, \n", "    \"STEP 02 - Processamento Básico\", \n", "    expected_min_cols=len(df_step01_raw.columns),\n", "    previous_df=df_step01_raw\n", ")\n", "\n", "print(f\"\\n📋 RESUMO STEP 02:\")\n", "print(f\"   ✅ Target criado corretamente\")\n", "print(f\"   📊 Shape após processamento: {df_step02_processed.shape}\")\n", "print(f\"   🎯 Distribuição do target:\")\n", "print(df_step02_processed['Target'].value_counts(normalize=True))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## STEP 03: CRIAÇÃO DE FEATURES TÉCNICAS"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📂 STEP 03: Criando features técnicas...\n", "📈 Criando indicadores técnicos...\n", "\n", "============================================================\n", "🔍 AUDITORIA: STEP 03 - FEATURES TÉCNICAS\n", "============================================================\n", "📊 INFORMAÇÕES BÁSICAS:\n", "   Shape: (3591, 44)\n", "   Memória: 1.47 MB\n", "\n", "📋 DETALHES DAS COLUNAS (.info()):\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3591 entries, 0 to 3590\n", "Data columns (total 44 columns):\n", " #   Column           Non-Null Count  Dtype         \n", "---  ------           --------------  -----         \n", " 0   Data             3591 non-null   datetime64[ns]\n", " 1   Último           3591 non-null   float64       \n", " 2   Abertura         3591 non-null   float64       \n", " 3   Máxima           3591 non-null   float64       \n", " 4   Mínima           3591 non-null   float64       \n", " 5   Vol.             3591 non-null   object        \n", " 6   Var%             3591 non-null   object        \n", " 7   Volume           3591 non-null   float64       \n", " 8   Variacao         3591 non-null   float64       \n", " 9   Target           3591 non-null   int64         \n", " 10  MA_5             3587 non-null   float64       \n", " 11  MA_10            3582 non-null   float64       \n", " 12  MA_20            3572 non-null   float64       \n", " 13  MA_50            3542 non-null   float64       \n", " 14  BB_Middle        3572 non-null   float64       \n", " 15  BB_Upper         3572 non-null   float64       \n", " 16  BB_Lower         3572 non-null   float64       \n", " 17  BB_Width         3572 non-null   float64       \n", " 18  BB_Position      3572 non-null   float64       \n", " 19  RSI              3578 non-null   float64       \n", " 20  MACD             3591 non-null   float64       \n", " 21  Signal_Line      3591 non-null   float64       \n", " 22  high_low         3591 non-null   float64       \n", " 23  high_close_prev  3590 non-null   float64       \n", " 24  low_close_prev   3590 non-null   float64       \n", " 25  true_range       3591 non-null   float64       \n", " 26  atr_5            3587 non-null   float64       \n", " 27  atr_10           3582 non-null   float64       \n", " 28  atr_20           3572 non-null   float64       \n", " 29  returns          3590 non-null   float64       \n", " 30  volatility_5     3586 non-null   float64       \n", " 31  volatility_10    3581 non-null   float64       \n", " 32  volatility_20    3571 non-null   float64       \n", " 33  Price_Range      3591 non-null   float64       \n", " 34  Price_Position   3591 non-null   float64       \n", " 35  Gap              3590 non-null   float64       \n", " 36  hl_close_ratio   3591 non-null   float64       \n", " 37  day_of_week      3591 non-null   int32         \n", " 38  month            3591 non-null   int32         \n", " 39  quarter          3591 non-null   int32         \n", " 40  year             3591 non-null   int32         \n", " 41  is_month_start   3591 non-null   int64         \n", " 42  is_month_end     3591 non-null   int64         \n", " 43  is_quarter_end   3591 non-null   int64         \n", "dtypes: datetime64[ns](1), float64(33), int32(4), int64(4), object(2)\n", "memory usage: 1.2+ MB\n", "\n", "📂 CATEGORIZAÇÃO DE FEATURES:\n", "   🏢 OHLC/Volume (5): ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Volume']\n", "   📅 Temporais (8): ['Data', 'day_of_week', 'month', 'quarter', 'year', 'is_month_start', 'is_month_end', 'is_quarter_end']\n", "   📈 Té<PERSON><PERSON>s (17): ['MA_5', 'MA_10', 'MA_20', 'MA_50', 'BB_Middle']...\n", "   ⏰ Shifted (0): []\n", "   🕯️ Candlestick (2): ['high_close_prev', 'low_close_prev']\n", "   🎯 Target (1): ['Target']\n", "   ❓ Outras (11): ['Vol.', 'Var%', 'Variacao', 'Signal_Line', 'high_low']...\n", "\n", "🚨 ANÁLISE DE MUDANÇAS:\n", "   📉 Features PERDIDAS (0):\n", "      ✅ Nenhuma feature perdida\n", "   📈 Features GANHAS (34):\n", "      ✅ BB_Lower\n", "      ✅ BB_Middle\n", "      ✅ BB_Position\n", "      ✅ BB_Upper\n", "      ✅ BB_Width\n", "      ✅ Gap\n", "      ✅ MACD\n", "      ✅ MA_10\n", "      ✅ MA_20\n", "      ✅ MA_5\n", "      ✅ MA_50\n", "      ✅ Price_Position\n", "      ✅ Price_Range\n", "      ✅ RSI\n", "      ✅ Signal_Line\n", "      ✅ atr_10\n", "      ✅ atr_20\n", "      ✅ atr_5\n", "      ✅ day_of_week\n", "      ✅ high_close_prev\n", "      ✅ high_low\n", "      ✅ hl_close_ratio\n", "      ✅ is_month_end\n", "      ✅ is_month_start\n", "      ✅ is_quarter_end\n", "      ✅ low_close_prev\n", "      ✅ month\n", "      ✅ quarter\n", "      ✅ returns\n", "      ✅ true_range\n", "      ✅ volatility_10\n", "      ✅ volatility_20\n", "      ✅ volatility_5\n", "      ✅ year\n", "   📊 Mudança líquida: +34 features\n", "\n", "🔍 QUALIDADE DOS DADOS:\n", "   Colunas com valores ausentes: 20\n", "   Top 5 com mais NaNs:\n", "      MA_5: 4 (0.1%)\n", "      MA_10: 9 (0.3%)\n", "      MA_20: 19 (0.5%)\n", "      MA_50: 49 (1.4%)\n", "      BB_Middle: 19 (0.5%)\n", "\n", "✅ Auditoria de STEP 03 - Features Técnicas concluída\n", "============================================================\n", "\n", "📋 RESUMO STEP 03:\n", "   ✅ Features técnicas criadas: 34\n", "   📊 Total de features: 44\n", "   🎯 Shape final: (3591, 44)\n"]}], "source": ["# STEP 03: CRIAÇÃO DE FEATURES TÉCNICAS\n", "print(\"\\n📂 STEP 03: Criando features técnicas...\")\n", "\n", "# Criar cópia para features\n", "df_step03_features = df_step02_processed.copy()\n", "\n", "print(\"📈 Criando indicadores técnicos...\")\n", "\n", "# M<PERSON><PERSON>s móveis\n", "for periodo in [5, 10, 20, 50]:\n", "    df_step03_features[f'MA_{periodo}'] = df_step03_features['Último'].rolling(window=periodo).mean()\n", "\n", "# Bandas de Bollinger\n", "df_step03_features['BB_Middle'] = df_step03_features['Último'].rolling(window=20).mean()\n", "bb_std = df_step03_features['Último'].rolling(window=20).std()\n", "df_step03_features['BB_Upper'] = df_step03_features['BB_Middle'] + (bb_std * 2)\n", "df_step03_features['BB_Lower'] = df_step03_features['BB_Middle'] - (bb_std * 2)\n", "df_step03_features['BB_Width'] = df_step03_features['BB_Upper'] - df_step03_features['BB_Lower']\n", "df_step03_features['BB_Position'] = (df_step03_features['Último'] - df_step03_features['BB_Lower']) / df_step03_features['BB_Width']\n", "\n", "# RSI\n", "def calculate_rsi(prices, window=14):\n", "    delta = prices.diff()\n", "    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()\n", "    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()\n", "    rs = gain / loss\n", "    return 100 - (100 / (1 + rs))\n", "\n", "df_step03_features['RSI'] = calculate_rsi(df_step03_features['Último'])\n", "\n", "# MACD\n", "ema_12 = df_step03_features['Último'].ewm(span=12).mean()\n", "ema_26 = df_step03_features['Último'].ewm(span=26).mean()\n", "df_step03_features['MACD'] = ema_12 - ema_26\n", "df_step03_features['Signal_Line'] = df_step03_features['MACD'].ewm(span=9).mean()\n", "\n", "# ATR (Average True Range)\n", "df_step03_features['high_low'] = df_step03_features['Máxima'] - df_step03_features['Mín<PERSON>']\n", "df_step03_features['high_close_prev'] = abs(df_step03_features['Máxima'] - df_step03_features['Último'].shift(1))\n", "df_step03_features['low_close_prev'] = abs(df_step03_features['Mínima'] - df_step03_features['Último'].shift(1))\n", "df_step03_features['true_range'] = df_step03_features[['high_low', 'high_close_prev', 'low_close_prev']].max(axis=1)\n", "\n", "for periodo in [5, 10, 20]:\n", "    df_step03_features[f'atr_{periodo}'] = df_step03_features['true_range'].rolling(window=periodo).mean()\n", "\n", "# Volatilidade\n", "df_step03_features['returns'] = df_step03_features['Último'].pct_change()\n", "for periodo in [5, 10, 20]:\n", "    df_step03_features[f'volatility_{periodo}'] = df_step03_features['returns'].rolling(window=periodo).std()\n", "\n", "# Features de preço\n", "df_step03_features['Price_Range'] = df_step03_features['Máxima'] - df_step03_features['Mínima']\n", "df_step03_features['Price_Position'] = (df_step03_features['Último'] - df_step03_features['Mínima']) / df_step03_features['Price_Range']\n", "df_step03_features['Gap'] = df_step03_features['Abertura'] - df_step03_features['Último'].shift(1)\n", "df_step03_features['hl_close_ratio'] = (df_step03_features['Máxima'] - df_step03_features['Mínima']) / df_step03_features['Último']\n", "\n", "# Features temporais\n", "df_step03_features['day_of_week'] = df_step03_features['Data'].dt.dayofweek\n", "df_step03_features['month'] = df_step03_features['Data'].dt.month\n", "df_step03_features['quarter'] = df_step03_features['Data'].dt.quarter\n", "df_step03_features['year'] = df_step03_features['Data'].dt.year\n", "df_step03_features['is_month_start'] = (df_step03_features['Data'].dt.day <= 5).astype(int)\n", "df_step03_features['is_month_end'] = (df_step03_features['Data'].dt.day >= 25).astype(int)\n", "df_step03_features['is_quarter_end'] = df_step03_features['Data'].dt.is_quarter_end.astype(int)\n", "\n", "# Auditoria da criação de features\n", "audit_step03 = audit_pipeline_step(\n", "    df_step03_features, \n", "    \"STEP 03 - Features Técnicas\", \n", "    expected_min_cols=30,  # Esperamos pelo menos 30 features\n", "    previous_df=df_step02_processed\n", ")\n", "\n", "print(f\"\\n📋 RESUMO STEP 03:\")\n", "print(f\"   ✅ Features técnicas criadas: {df_step03_features.shape[1] - df_step02_processed.shape[1]}\")\n", "print(f\"   📊 Total de features: {df_step03_features.shape[1]}\")\n", "print(f\"   🎯 Shape final: {df_step03_features.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## STEP 04: SELEÇÃO DE FEATURES (PONTO CRÍTICO)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📂 STEP 04: Seleção de features (PONTO CRÍTICO)...\n", "🚨 ATENÇÃO: Esta é uma área comum para 'PENHASCOS' de features!\n", "\n", "🔍 CHECKPOINT ANTES DA SELEÇÃO:\n", "   Features disponíveis: 44\n", "   Primeiras 10: ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Vol.', 'Var%', 'Volume', 'Variacao', 'Target']\n", "   Últimas 10: ['Price_Position', 'Gap', 'hl_close_ratio', 'day_of_week', 'month', 'quarter', 'year', 'is_month_start', 'is_month_end', 'is_quarter_end']\n", "\n", "🗑️ Removendo apenas colunas auxiliares: ['Vol.', 'Var%', 'high_low', 'BB_Middle']\n", "\n", "✅ Todas as features importantes preservadas\n", "\n", "============================================================\n", "🔍 AUDITORIA: STEP 04 - SELEÇÃO DE FEATURES\n", "============================================================\n", "📊 INFORMAÇÕES BÁSICAS:\n", "   Shape: (3591, 40)\n", "   Memória: 1.04 MB\n", "\n", "📋 DETALHES DAS COLUNAS (.info()):\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3591 entries, 0 to 3590\n", "Data columns (total 40 columns):\n", " #   Column           Non-Null Count  Dtype         \n", "---  ------           --------------  -----         \n", " 0   Data             3591 non-null   datetime64[ns]\n", " 1   Último           3591 non-null   float64       \n", " 2   Abertura         3591 non-null   float64       \n", " 3   Máxima           3591 non-null   float64       \n", " 4   Mínima           3591 non-null   float64       \n", " 5   Volume           3591 non-null   float64       \n", " 6   Variacao         3591 non-null   float64       \n", " 7   Target           3591 non-null   int64         \n", " 8   MA_5             3587 non-null   float64       \n", " 9   MA_10            3582 non-null   float64       \n", " 10  MA_20            3572 non-null   float64       \n", " 11  MA_50            3542 non-null   float64       \n", " 12  BB_Upper         3572 non-null   float64       \n", " 13  BB_Lower         3572 non-null   float64       \n", " 14  BB_Width         3572 non-null   float64       \n", " 15  BB_Position      3572 non-null   float64       \n", " 16  RSI              3578 non-null   float64       \n", " 17  MACD             3591 non-null   float64       \n", " 18  Signal_Line      3591 non-null   float64       \n", " 19  high_close_prev  3590 non-null   float64       \n", " 20  low_close_prev   3590 non-null   float64       \n", " 21  true_range       3591 non-null   float64       \n", " 22  atr_5            3587 non-null   float64       \n", " 23  atr_10           3582 non-null   float64       \n", " 24  atr_20           3572 non-null   float64       \n", " 25  returns          3590 non-null   float64       \n", " 26  volatility_5     3586 non-null   float64       \n", " 27  volatility_10    3581 non-null   float64       \n", " 28  volatility_20    3571 non-null   float64       \n", " 29  Price_Range      3591 non-null   float64       \n", " 30  Price_Position   3591 non-null   float64       \n", " 31  Gap              3590 non-null   float64       \n", " 32  hl_close_ratio   3591 non-null   float64       \n", " 33  day_of_week      3591 non-null   int32         \n", " 34  month            3591 non-null   int32         \n", " 35  quarter          3591 non-null   int32         \n", " 36  year             3591 non-null   int32         \n", " 37  is_month_start   3591 non-null   int64         \n", " 38  is_month_end     3591 non-null   int64         \n", " 39  is_quarter_end   3591 non-null   int64         \n", "dtypes: datetime64[ns](1), float64(31), int32(4), int64(4)\n", "memory usage: 1.0 MB\n", "\n", "📂 CATEGORIZAÇÃO DE FEATURES:\n", "   🏢 OHLC/Volume (5): ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Volume']\n", "   📅 Temporais (8): ['Data', 'day_of_week', 'month', 'quarter', 'year', 'is_month_start', 'is_month_end', 'is_quarter_end']\n", "   📈 Técnicas (16): ['MA_5', 'MA_10', 'MA_20', 'MA_50', 'BB_Upper']...\n", "   ⏰ Shifted (0): []\n", "   🕯️ Candlestick (2): ['high_close_prev', 'low_close_prev']\n", "   🎯 Target (1): ['Target']\n", "   ❓ Outras (8): ['Variacao', 'Signal_Line', 'true_range', 'returns', 'Price_Range']...\n", "\n", "🚨 ANÁLISE DE MUDANÇAS:\n", "   📉 Features PERDIDAS (4):\n", "      ❌ BB_Middle\n", "      ❌ Var%\n", "      ❌ Vol.\n", "      ❌ high_low\n", "   📈 Features GANHAS (0):\n", "      ➖ Nenhuma feature ganha\n", "   📊 Mudança líquida: -4 features\n", "\n", "🔍 QUALIDADE DOS DADOS:\n", "   Colunas com valores ausentes: 19\n", "   Top 5 com mais NaNs:\n", "      MA_5: 4 (0.1%)\n", "      MA_10: 9 (0.3%)\n", "      MA_20: 19 (0.5%)\n", "      MA_50: 49 (1.4%)\n", "      BB_Upper: 19 (0.5%)\n", "\n", "✅ Auditoria de STEP 04 - Seleção de Features concluída\n", "============================================================\n", "\n", "📋 RESUMO STEP 04:\n", "   ✅ Features selecionadas: 40\n", "   📊 Features removidas: 4\n", "   🎯 Shape final: (3591, 40)\n"]}], "source": ["# STEP 04: SELEÇÃO DE FEATURES - PONTO CRÍTICO!\n", "print(\"\\n📂 STEP 04: Seleção de features (PONTO CRÍTICO)...\")\n", "print(\"🚨 ATENÇÃO: Esta é uma área comum para 'PENHASCOS' de features!\")\n", "\n", "# ANTES da seleção - checkpoint cr<PERSON><PERSON><PERSON>\n", "print(f\"\\n🔍 CHECKPOINT ANTES DA SELEÇÃO:\")\n", "print(f\"   Features disponíveis: {df_step03_features.shape[1]}\")\n", "print(f\"   Primeiras 10: {list(df_step03_features.columns)[:10]}\")\n", "print(f\"   Últimas 10: {list(df_step03_features.columns)[-10:]}\")\n", "\n", "# Criar cópia para seleção\n", "df_step04_selected = df_step03_features.copy()\n", "\n", "# ESTRATÉGIA CONSERVADORA: Manter TODAS as features criadas\n", "# Apenas remover colunas auxiliares e duplicadas\n", "columns_to_remove = [\n", "    'Vol.',  # V<PERSON><PERSON> string do volume\n", "    'Var%',  # <PERSON>ers<PERSON> string da variação\n", "    'high_low',  # Auxiliar para ATR\n", "    'BB_Middle'  # Duplicata da MA_20\n", "]\n", "\n", "# Remover apenas colunas auxiliares\n", "columns_to_remove_existing = [col for col in columns_to_remove if col in df_step04_selected.columns]\n", "if columns_to_remove_existing:\n", "    print(f\"\\n🗑️ Removendo apenas colunas auxiliares: {columns_to_remove_existing}\")\n", "    df_step04_selected = df_step04_selected.drop(columns=columns_to_remove_existing)\n", "else:\n", "    print(f\"\\n✅ Nenhuma coluna auxiliar para remover\")\n", "\n", "# CHECKPOINT CRÍTICO: Verificar se não perdemos features importantes\n", "features_importantes = [\n", "    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>lt<PERSON>', 'Volume',\n", "    'MA_5', 'MA_10', 'MA_20', 'MA_50',\n", "    'BB_Upper', 'BB_Lower', 'BB_Width', 'BB_Position',\n", "    'RSI', 'MACD', 'Signal_Line',\n", "    'atr_5', 'atr_10', 'atr_20',\n", "    'volatility_5', 'volatility_10', 'volatility_20',\n", "    'Price_Range', 'Price_Position', 'Gap', 'hl_close_ratio',\n", "    'day_of_week', 'month', 'quarter', 'year',\n", "    'is_month_start', 'is_month_end', 'is_quarter_end',\n", "    'Target'\n", "]\n", "\n", "features_perdidas = [f for f in features_importantes if f not in df_step04_selected.columns]\n", "if features_perdidas:\n", "    print(f\"\\n🚨 ALERTA: Features importantes perdidas: {features_perdidas}\")\n", "else:\n", "    print(f\"\\n✅ Todas as features importantes preservadas\")\n", "\n", "# Auditoria da seleção\n", "audit_step04 = audit_pipeline_step(\n", "    df_step04_selected, \n", "    \"STEP 04 - Seleção de Features\", \n", "    expected_min_cols=len(features_importantes),\n", "    previous_df=df_step03_features\n", ")\n", "\n", "print(f\"\\n📋 RESUMO STEP 04:\")\n", "print(f\"   ✅ Features selecionadas: {df_step04_selected.shape[1]}\")\n", "print(f\"   📊 Features removidas: {df_step03_features.shape[1] - df_step04_selected.shape[1]}\")\n", "print(f\"   🎯 Shape final: {df_step04_selected.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## STEP 05: CORREÇÃO TEMPORAL (PENHASCO CRÍTICO!)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📂 STEP 05: Correção temporal (PENHASCO CRÍTICO!)...\n", "🚨 ATENÇÃO: Esta é a área mais provável para perda massiva de features!\n", "\n", "🔍 CHECKPOINT ANTES DA CORREÇÃO TEMPORAL:\n", "   Features disponíveis: 40\n", "   Shape: (3591, 40)\n", "\n", "📊 ANÁLISE DE FEATURES PARA SHIFT:\n", "   Features para shift: 31 de 31\n", "   Features sem shift: 9 de 9\n", "\n", "⏰ Aplicando shift temporal...\n", "\n", "🔍 CHECKPOINT APÓS CORREÇÃO TEMPORAL:\n", "   Features antes: 40\n", "   Features depois: 40\n", "   Mudança: +0\n", "\n", "📊 VERIFICAÇÃO DE FEATURES SHIFTED:\n", "   Esperadas: 31\n", "   Criadas: 31\n", "   ✅ Todas as features shifted criadas corretamente\n", "\n", "============================================================\n", "🔍 AUDITORIA: STEP 05 - CORREÇÃO TEMPORAL\n", "============================================================\n", "📊 INFORMAÇÕES BÁSICAS:\n", "   Shape: (3590, 40)\n", "   Memória: 1.04 MB\n", "\n", "📋 DETALHES DAS COLUNAS (.info()):\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3590 entries, 0 to 3589\n", "Data columns (total 40 columns):\n", " #   Column                   Non-Null Count  Dtype         \n", "---  ------                   --------------  -----         \n", " 0   Data                     3590 non-null   datetime64[ns]\n", " 1   day_of_week              3590 non-null   int32         \n", " 2   month                    3590 non-null   int32         \n", " 3   quarter                  3590 non-null   int32         \n", " 4   year                     3590 non-null   int32         \n", " 5   is_month_start           3590 non-null   int64         \n", " 6   is_month_end             3590 non-null   int64         \n", " 7   is_quarter_end           3590 non-null   int64         \n", " 8   Target                   3590 non-null   int64         \n", " 9   Abertura_shifted         3590 non-null   float64       \n", " 10  Máxima_shifted           3590 non-null   float64       \n", " 11  <PERSON><PERSON><PERSON>_shifted           3590 non-null   float64       \n", " 12  Último_shifted           3590 non-null   float64       \n", " 13  Volume_shifted           3590 non-null   float64       \n", " 14  MA_5_shifted             3586 non-null   float64       \n", " 15  MA_10_shifted            3581 non-null   float64       \n", " 16  MA_20_shifted            3571 non-null   float64       \n", " 17  MA_50_shifted            3541 non-null   float64       \n", " 18  BB_Upper_shifted         3571 non-null   float64       \n", " 19  BB_Lower_shifted         3571 non-null   float64       \n", " 20  BB_Width_shifted         3571 non-null   float64       \n", " 21  BB_Position_shifted      3571 non-null   float64       \n", " 22  RSI_shifted              3577 non-null   float64       \n", " 23  MACD_shifted             3590 non-null   float64       \n", " 24  Signal_Line_shifted      3590 non-null   float64       \n", " 25  atr_5_shifted            3586 non-null   float64       \n", " 26  atr_10_shifted           3581 non-null   float64       \n", " 27  atr_20_shifted           3571 non-null   float64       \n", " 28  volatility_5_shifted     3585 non-null   float64       \n", " 29  volatility_10_shifted    3580 non-null   float64       \n", " 30  volatility_20_shifted    3570 non-null   float64       \n", " 31  Price_Range_shifted      3590 non-null   float64       \n", " 32  Price_Position_shifted   3590 non-null   float64       \n", " 33  Gap_shifted              3589 non-null   float64       \n", " 34  hl_close_ratio_shifted   3590 non-null   float64       \n", " 35  true_range_shifted       3590 non-null   float64       \n", " 36  high_close_prev_shifted  3589 non-null   float64       \n", " 37  low_close_prev_shifted   3589 non-null   float64       \n", " 38  returns_shifted          3589 non-null   float64       \n", " 39  Variacao_shifted         3590 non-null   float64       \n", "dtypes: datetime64[ns](1), float64(31), int32(4), int64(4)\n", "memory usage: 1.0 MB\n", "\n", "📂 CATEGORIZAÇÃO DE FEATURES:\n", "   🏢 OHLC/Volume (0): []\n", "   📅 Temporais (8): ['Data', 'day_of_week', 'month', 'quarter', 'year', 'is_month_start', 'is_month_end', 'is_quarter_end']\n", "   📈 <PERSON><PERSON><PERSON><PERSON><PERSON> (18): ['<PERSON><PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted', 'MA_5_shifted', 'MA_10_shifted', 'MA_20_shifted']...\n", "   ⏰ Shifted (31): ['<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted', 'Último_shifted', 'Volume_shifted']...\n", "   🕯️ Candlestick (2): ['high_close_prev_shifted', 'low_close_prev_shifted']\n", "   🎯 Target (1): ['Target']\n", "   ❓ Outras (0): []\n", "\n", "🚨 ANÁLISE DE MUDANÇAS:\n", "   📉 Features PERDIDAS (31):\n", "      ❌ Abertura\n", "      ❌ BB_Lower\n", "      ❌ BB_Position\n", "      ❌ BB_Upper\n", "      ❌ BB_Width\n", "      ❌ Gap\n", "      ❌ MACD\n", "      ❌ MA_10\n", "      ❌ MA_20\n", "      ❌ MA_5\n", "      ❌ MA_50\n", "      ❌ Máxima\n", "      ❌ <PERSON><PERSON><PERSON>\n", "      ❌ Price_Position\n", "      ❌ Price_Range\n", "      ❌ RSI\n", "      ❌ Signal_Line\n", "      ❌ Variacao\n", "      ❌ Volume\n", "      ❌ atr_10\n", "      ❌ atr_20\n", "      ❌ atr_5\n", "      ❌ high_close_prev\n", "      ❌ hl_close_ratio\n", "      ❌ low_close_prev\n", "      ❌ returns\n", "      ❌ true_range\n", "      ❌ volatility_10\n", "      ❌ volatility_20\n", "      ❌ volatility_5\n", "      ❌ Último\n", "   📈 Features GANHAS (31):\n", "      ✅ Abertura_shifted\n", "      ✅ BB_Lower_shifted\n", "      ✅ BB_Position_shifted\n", "      ✅ BB_Upper_shifted\n", "      ✅ BB_Width_shifted\n", "      ✅ Gap_shifted\n", "      ✅ MACD_shifted\n", "      ✅ MA_10_shifted\n", "      ✅ MA_20_shifted\n", "      ✅ MA_50_shifted\n", "      ✅ MA_5_shifted\n", "      ✅ Máxima_shifted\n", "      ✅ <PERSON><PERSON><PERSON>_shifted\n", "      ✅ Price_Position_shifted\n", "      ✅ Price_Range_shifted\n", "      ✅ RSI_shifted\n", "      ✅ Signal_Line_shifted\n", "      ✅ Variacao_shifted\n", "      ✅ Volume_shifted\n", "      ✅ atr_10_shifted\n", "      ✅ atr_20_shifted\n", "      ✅ atr_5_shifted\n", "      ✅ high_close_prev_shifted\n", "      ✅ hl_close_ratio_shifted\n", "      ✅ low_close_prev_shifted\n", "      ✅ returns_shifted\n", "      ✅ true_range_shifted\n", "      ✅ volatility_10_shifted\n", "      ✅ volatility_20_shifted\n", "      ✅ volatility_5_shifted\n", "      ✅ Último_shifted\n", "   📊 Mudança líquida: +0 features\n", "\n", "🚨 ALERTA: PERDA SIGNIFICATIVA DE FEATURES!\n", "   31 features foram perdidas nesta etapa\n", "   <PERSON><PERSON> pode indicar um 'PENHASCO' no pipeline\n", "\n", "🔍 QUALIDADE DOS DADOS:\n", "   Colunas com valores ausentes: 19\n", "   Top 5 com mais NaNs:\n", "      MA_5_shifted: 4 (0.1%)\n", "      MA_10_shifted: 9 (0.3%)\n", "      MA_20_shifted: 19 (0.5%)\n", "      MA_50_shifted: 49 (1.4%)\n", "      BB_Upper_shifted: 19 (0.5%)\n", "\n", "✅ Auditoria de STEP 05 - Correção Temporal concluída\n", "============================================================\n", "\n", "📋 RESUMO STEP 05:\n", "   ✅ Correção temporal aplicada\n", "   📊 Features shifted criadas: 31\n", "   🎯 Shape final: (3590, 40)\n"]}], "source": ["# STEP 05: CORREÇÃO TEMPORAL - PENHASCO CRÍTICO!\n", "print(\"\\n📂 STEP 05: Correção temporal (PENHASCO CRÍTICO!)...\")\n", "print(\"🚨 ATENÇÃO: Esta é a área mais provável para perda massiva de features!\")\n", "\n", "# CHECKPOINT ANTES DA CORREÇÃO TEMPORAL\n", "print(f\"\\n🔍 CHECKPOINT ANTES DA CORREÇÃO TEMPORAL:\")\n", "print(f\"   Features disponíveis: {df_step04_selected.shape[1]}\")\n", "print(f\"   Shape: {df_step04_selected.shape}\")\n", "\n", "# Criar cópia para correção temporal\n", "df_step05_temporal = df_step04_selected.copy()\n", "\n", "# ESTRATÉGIA CONSERVADORA: Aplicar shift apenas onde necessário\n", "# Identificar features que precisam de shift (dados do presente/futuro)\n", "features_to_shift = [\n", "    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>lt<PERSON>', 'Volume',\n", "    'MA_5', 'MA_10', 'MA_20', 'MA_50',\n", "    'BB_Upper', 'BB_Lower', 'BB_Width', 'BB_Position',\n", "    'RSI', 'MACD', 'Signal_Line',\n", "    'atr_5', 'atr_10', 'atr_20',\n", "    'volatility_5', 'volatility_10', 'volatility_20',\n", "    'Price_Range', 'Price_Position', 'Gap', 'hl_close_ratio',\n", "    'true_range', 'high_close_prev', 'low_close_prev',\n", "    'returns', 'Variacao'\n", "]\n", "\n", "# Features que NÃO precisam de shift (dados temporais do passado)\n", "features_no_shift = [\n", "    'Data', 'day_of_week', 'month', 'quarter', 'year',\n", "    'is_month_start', 'is_month_end', 'is_quarter_end',\n", "    'Target'\n", "]\n", "\n", "# Verificar quais features est<PERSON> disponíveis\n", "available_to_shift = [f for f in features_to_shift if f in df_step05_temporal.columns]\n", "available_no_shift = [f for f in features_no_shift if f in df_step05_temporal.columns]\n", "\n", "print(f\"\\n📊 ANÁLISE DE FEATURES PARA SHIFT:\")\n", "print(f\"   Features para shift: {len(available_to_shift)} de {len(features_to_shift)}\")\n", "print(f\"   Features sem shift: {len(available_no_shift)} de {len(features_no_shift)}\")\n", "\n", "missing_to_shift = [f for f in features_to_shift if f not in df_step05_temporal.columns]\n", "if missing_to_shift:\n", "    print(f\"   ⚠️ Features ausentes para shift: {missing_to_shift}\")\n", "\n", "# APLICAR SHIFT DE FORMA CONSERVADORA\n", "print(f\"\\n⏰ Aplicando shift temporal...\")\n", "\n", "# Criar dataset final com features shifted\n", "df_temporal_final = pd.DataFrame()\n", "\n", "# 1. <PERSON><PERSON> features temporais sem shift\n", "for feature in available_no_shift:\n", "    df_temporal_final[feature] = df_step05_temporal[feature]\n", "\n", "# 2. Aplicar shift nas features de preço/indicadores\n", "for feature in available_to_shift:\n", "    df_temporal_final[f'{feature}_shifted'] = df_step05_temporal[feature].shift(1)\n", "\n", "# 3. Remover primeira linha (NaN devido ao shift)\n", "df_temporal_final = df_temporal_final.iloc[1:].reset_index(drop=True)\n", "\n", "# CHECKPOINT CRÍTICO APÓS CORREÇÃO TEMPORAL\n", "print(f\"\\n🔍 CHECKPOINT APÓS CORREÇÃO TEMPORAL:\")\n", "print(f\"   Features antes: {df_step05_temporal.shape[1]}\")\n", "print(f\"   Features depois: {df_temporal_final.shape[1]}\")\n", "print(f\"   Mudança: {df_temporal_final.shape[1] - df_step05_temporal.shape[1]:+d}\")\n", "\n", "# Verificar se temos as features shifted esperadas\n", "expected_shifted = [f'{f}_shifted' for f in available_to_shift]\n", "actual_shifted = [col for col in df_temporal_final.columns if col.endswith('_shifted')]\n", "\n", "print(f\"\\n📊 VERIFICAÇÃO DE FEATURES SHIFTED:\")\n", "print(f\"   Esperadas: {len(expected_shifted)}\")\n", "print(f\"   Criadas: {len(actual_shifted)}\")\n", "\n", "missing_shifted = [f for f in expected_shifted if f not in df_temporal_final.columns]\n", "if missing_shifted:\n", "    print(f\"   ❌ Features shifted ausentes: {missing_shifted[:5]}{'...' if len(missing_shifted) > 5 else ''}\")\n", "else:\n", "    print(f\"   ✅ Todas as features shifted criadas corretamente\")\n", "\n", "# Atualizar variável para próxima etapa\n", "df_step05_temporal = df_temporal_final.copy()\n", "\n", "# Auditoria da correção temporal\n", "audit_step05 = audit_pipeline_step(\n", "    df_step05_temporal, \n", "    \"STEP 05 - Correção Temporal\", \n", "    expected_min_cols=len(available_no_shift) + len(available_to_shift),\n", "    previous_df=df_step04_selected\n", ")\n", "\n", "print(f\"\\n📋 RESUMO STEP 05:\")\n", "print(f\"   ✅ Correção temporal aplicada\")\n", "print(f\"   📊 Features shifted criadas: {len(actual_shifted)}\")\n", "print(f\"   🎯 Shape final: {df_step05_temporal.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## STEP 06: PADRÕES DE CANDLESTICK"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📂 STEP 06: <PERSON><PERSON><PERSON> de candlestick...\n", "\n", "🕯️ VERIFICAÇÃO DE COLUNAS OHLC SHIFTED:\n", "   Necessárias: ['<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted']\n", "   Disponíveis: ['<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted']\n", "   ✅ <PERSON><PERSON> as colunas OHLC shifted disponíveis\n", "\n", "📊 Criando padr<PERSON>es de candlestick...\n", "   ✅ 9 features de candlestick criadas\n", "\n", "============================================================\n", "🔍 AUDITORIA: STEP 06 - PADRÕES DE CANDLESTICK\n", "============================================================\n", "📊 INFORMAÇÕES BÁSICAS:\n", "   Shape: (3590, 49)\n", "   Memória: 1.29 MB\n", "\n", "📋 DETALHES DAS COLUNAS (.info()):\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3590 entries, 0 to 3589\n", "Data columns (total 49 columns):\n", " #   Column                   Non-Null Count  Dtype         \n", "---  ------                   --------------  -----         \n", " 0   Data                     3590 non-null   datetime64[ns]\n", " 1   day_of_week              3590 non-null   int32         \n", " 2   month                    3590 non-null   int32         \n", " 3   quarter                  3590 non-null   int32         \n", " 4   year                     3590 non-null   int32         \n", " 5   is_month_start           3590 non-null   int64         \n", " 6   is_month_end             3590 non-null   int64         \n", " 7   is_quarter_end           3590 non-null   int64         \n", " 8   Target                   3590 non-null   int64         \n", " 9   Abertura_shifted         3590 non-null   float64       \n", " 10  Máxima_shifted           3590 non-null   float64       \n", " 11  <PERSON><PERSON><PERSON>_shifted           3590 non-null   float64       \n", " 12  Último_shifted           3590 non-null   float64       \n", " 13  Volume_shifted           3590 non-null   float64       \n", " 14  MA_5_shifted             3586 non-null   float64       \n", " 15  MA_10_shifted            3581 non-null   float64       \n", " 16  MA_20_shifted            3571 non-null   float64       \n", " 17  MA_50_shifted            3541 non-null   float64       \n", " 18  BB_Upper_shifted         3571 non-null   float64       \n", " 19  BB_Lower_shifted         3571 non-null   float64       \n", " 20  BB_Width_shifted         3571 non-null   float64       \n", " 21  BB_Position_shifted      3571 non-null   float64       \n", " 22  RSI_shifted              3577 non-null   float64       \n", " 23  MACD_shifted             3590 non-null   float64       \n", " 24  Signal_Line_shifted      3590 non-null   float64       \n", " 25  atr_5_shifted            3586 non-null   float64       \n", " 26  atr_10_shifted           3581 non-null   float64       \n", " 27  atr_20_shifted           3571 non-null   float64       \n", " 28  volatility_5_shifted     3585 non-null   float64       \n", " 29  volatility_10_shifted    3580 non-null   float64       \n", " 30  volatility_20_shifted    3570 non-null   float64       \n", " 31  Price_Range_shifted      3590 non-null   float64       \n", " 32  Price_Position_shifted   3590 non-null   float64       \n", " 33  Gap_shifted              3589 non-null   float64       \n", " 34  hl_close_ratio_shifted   3590 non-null   float64       \n", " 35  true_range_shifted       3590 non-null   float64       \n", " 36  high_close_prev_shifted  3589 non-null   float64       \n", " 37  low_close_prev_shifted   3589 non-null   float64       \n", " 38  returns_shifted          3589 non-null   float64       \n", " 39  Variacao_shifted         3590 non-null   float64       \n", " 40  doji_prev                3590 non-null   int64         \n", " 41  hammer_prev              3590 non-null   int64         \n", " 42  shooting_star_prev       3590 non-null   int64         \n", " 43  engulfing_bullish_prev   3590 non-null   int64         \n", " 44  bullish_candle_prev      3590 non-null   int64         \n", " 45  bearish_candle_prev      3590 non-null   int64         \n", " 46  body_size_prev           3590 non-null   float64       \n", " 47  upper_shadow_prev        3590 non-null   float64       \n", " 48  lower_shadow_prev        3590 non-null   float64       \n", "dtypes: datetime64[ns](1), float64(34), int32(4), int64(10)\n", "memory usage: 1.3 MB\n", "\n", "📂 CATEGORIZAÇÃO DE FEATURES:\n", "   🏢 OHLC/Volume (0): []\n", "   📅 Temporais (8): ['Data', 'day_of_week', 'month', 'quarter', 'year', 'is_month_start', 'is_month_end', 'is_quarter_end']\n", "   📈 <PERSON><PERSON><PERSON><PERSON><PERSON> (18): ['<PERSON><PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted', 'MA_5_shifted', 'MA_10_shifted', 'MA_20_shifted']...\n", "   ⏰ Shifted (31): ['<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted', 'Último_shifted', 'Volume_shifted']...\n", "   🕯️ Candlestick (11): ['high_close_prev_shifted', 'low_close_prev_shifted', 'doji_prev', 'hammer_prev', 'shooting_star_prev']...\n", "   🎯 Target (1): ['Target']\n", "   ❓ Outras (0): []\n", "\n", "🚨 ANÁLISE DE MUDANÇAS:\n", "   📉 Features PERDIDAS (0):\n", "      ✅ Nenhuma feature perdida\n", "   📈 Features GANHAS (9):\n", "      ✅ bearish_candle_prev\n", "      ✅ body_size_prev\n", "      ✅ bullish_candle_prev\n", "      ✅ doji_prev\n", "      ✅ engulfing_bullish_prev\n", "      ✅ hammer_prev\n", "      ✅ lower_shadow_prev\n", "      ✅ shooting_star_prev\n", "      ✅ upper_shadow_prev\n", "   📊 Mudança líquida: +9 features\n", "\n", "🔍 QUALIDADE DOS DADOS:\n", "   Colunas com valores ausentes: 19\n", "   Top 5 com mais NaNs:\n", "      MA_5_shifted: 4 (0.1%)\n", "      MA_10_shifted: 9 (0.3%)\n", "      MA_20_shifted: 19 (0.5%)\n", "      MA_50_shifted: 49 (1.4%)\n", "      BB_Upper_shifted: 19 (0.5%)\n", "\n", "✅ Auditoria de STEP 06 - Padrões de Candlestick concluída\n", "============================================================\n", "\n", "📋 RESUMO STEP 06:\n", "   ✅ Features de candlestick criadas: 9\n", "   📊 Total de features: 49\n", "   🎯 Shape final: (3590, 49)\n"]}], "source": ["# STEP 06: PADRÕES DE CANDLESTICK\n", "print(\"\\n📂 STEP 06: <PERSON><PERSON><PERSON> de candlestick...\")\n", "\n", "# Criar cópia para candlestick\n", "df_step06_candlestick = df_step05_temporal.copy()\n", "\n", "# Verificar se temos as colunas OHLC shifted necessárias\n", "ohlc_shifted_cols = ['<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted']\n", "available_ohlc_shifted = [col for col in ohlc_shifted_cols if col in df_step06_candlestick.columns]\n", "\n", "print(f\"\\n🕯️ VERIFICAÇÃO DE COLUNAS OHLC SHIFTED:\")\n", "print(f\"   Necessárias: {ohlc_shifted_cols}\")\n", "print(f\"   Disponíveis: {available_ohlc_shifted}\")\n", "\n", "if len(available_ohlc_shifted) == 4:\n", "    print(f\"   ✅ Todas as colunas OHLC shifted disponíveis\")\n", "    \n", "    # Renomear temporariamente para facilitar cál<PERSON>los\n", "    ohlc_temp = df_step06_candlestick[available_ohlc_shifted].copy()\n", "    ohlc_temp.columns = ['Open', 'High', 'Low', 'Close']\n", "    \n", "    # Funções de padrões de candlestick\n", "    def detect_doji(df, threshold=0.1):\n", "        body_size = abs(df['Close'] - df['Open'])\n", "        total_range = df['High'] - df['Low']\n", "        return (body_size / total_range < threshold).astype(int)\n", "    \n", "    def detect_hammer(df):\n", "        body_size = abs(df['Close'] - df['Open'])\n", "        lower_shadow = df[['Open', 'Close']].min(axis=1) - df['Low']\n", "        upper_shadow = df['High'] - df[['Open', 'Close']].max(axis=1)\n", "        return ((lower_shadow > 2 * body_size) & (upper_shadow < body_size)).astype(int)\n", "    \n", "    def detect_shooting_star(df):\n", "        body_size = abs(df['Close'] - df['Open'])\n", "        lower_shadow = df[['Open', 'Close']].min(axis=1) - df['Low']\n", "        upper_shadow = df['High'] - df[['Open', 'Close']].max(axis=1)\n", "        return ((upper_shadow > 2 * body_size) & (lower_shadow < body_size)).astype(int)\n", "    \n", "    def detect_engulfing_bullish(df):\n", "        current_bullish = df['Close'] > df['Open']\n", "        prev_bearish = (df['Close'].shift(1) < df['Open'].shift(1))\n", "        current_open_below_prev_close = df['Open'] < df['Close'].shift(1)\n", "        current_close_above_prev_open = df['Close'] > df['Open'].shift(1)\n", "        return (current_bullish & prev_bearish & current_open_below_prev_close & current_close_above_prev_open).astype(int)\n", "    \n", "    # Aplicar pad<PERSON><PERSON><PERSON>\n", "    print(f\"\\n📊 Criando padrões de candlestick...\")\n", "    \n", "    df_step06_candlestick['doji_prev'] = detect_doji(ohlc_temp)\n", "    df_step06_candlestick['hammer_prev'] = detect_hammer(ohlc_temp)\n", "    df_step06_candlestick['shooting_star_prev'] = detect_shooting_star(ohlc_temp)\n", "    df_step06_candlestick['engulfing_bullish_prev'] = detect_engulfing_bullish(ohlc_temp)\n", "    \n", "    # Padrões adicionais\n", "    df_step06_candlestick['bullish_candle_prev'] = (ohlc_temp['Close'] > ohlc_temp['Open']).astype(int)\n", "    df_step06_candlestick['bearish_candle_prev'] = (ohlc_temp['Close'] < ohlc_temp['Open']).astype(int)\n", "    \n", "    # Métricas de candlestick\n", "    df_step06_candlestick['body_size_prev'] = abs(ohlc_temp['Close'] - ohlc_temp['Open'])\n", "    df_step06_candlestick['upper_shadow_prev'] = ohlc_temp['High'] - ohlc_temp[['Open', 'Close']].max(axis=1)\n", "    df_step06_candlestick['lower_shadow_prev'] = ohlc_temp[['Open', 'Close']].min(axis=1) - ohlc_temp['Low']\n", "    \n", "    candlestick_features_created = 9\n", "    print(f\"   ✅ {candlestick_features_created} features de candlestick criadas\")\n", "    \n", "else:\n", "    print(f\"   ❌ Colunas OHLC shifted insuficientes\")\n", "    print(f\"   Ausentes: {[col for col in ohlc_shifted_cols if col not in df_step06_candlestick.columns]}\")\n", "    candlestick_features_created = 0\n", "\n", "# Auditoria dos padrões de candlestick\n", "audit_step06 = audit_pipeline_step(\n", "    df_step06_candlestick, \n", "    \"STEP 06 - <PERSON><PERSON><PERSON><PERSON> de Candlestick\", \n", "    expected_min_cols=df_step05_temporal.shape[1] + candlestick_features_created,\n", "    previous_df=df_step05_temporal\n", ")\n", "\n", "print(f\"\\n📋 RESUMO STEP 06:\")\n", "print(f\"   ✅ Features de candlestick criadas: {candlestick_features_created}\")\n", "print(f\"   📊 Total de features: {df_step06_candlestick.shape[1]}\")\n", "print(f\"   🎯 Shape final: {df_step06_candlestick.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## STEP 07: <PERSON><PERSON><PERSON><PERSON> FINAL E AUDITORIA COMPLETA"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📂 STEP 07: Preparando dataset final...\n", "\n", "🧹 Limpeza final de dados...\n", "   <PERSON><PERSON>pe antes da limpeza: (3590, 49)\n", "   <PERSON><PERSON>pe a<PERSON><PERSON> limpeza: (3586, 49)\n", "   <PERSON><PERSON> removidas: 4\n", "\n", "============================================================\n", "🔍 AUDITORIA: STEP 07 - DATASET FINAL\n", "============================================================\n", "📊 INFORMAÇÕES BÁSICAS:\n", "   Shape: (3586, 49)\n", "   Memória: 1.31 MB\n", "\n", "📋 DETALHES DAS COLUNAS (.info()):\n", "<class 'pandas.core.frame.DataFrame'>\n", "Index: 3586 entries, 4 to 3589\n", "Data columns (total 49 columns):\n", " #   Column                   Non-Null Count  Dtype         \n", "---  ------                   --------------  -----         \n", " 0   Data                     3586 non-null   datetime64[ns]\n", " 1   day_of_week              3586 non-null   int32         \n", " 2   month                    3586 non-null   int32         \n", " 3   quarter                  3586 non-null   int32         \n", " 4   year                     3586 non-null   int32         \n", " 5   is_month_start           3586 non-null   int64         \n", " 6   is_month_end             3586 non-null   int64         \n", " 7   is_quarter_end           3586 non-null   int64         \n", " 8   Target                   3586 non-null   int64         \n", " 9   Abertura_shifted         3586 non-null   float64       \n", " 10  Máxima_shifted           3586 non-null   float64       \n", " 11  <PERSON><PERSON><PERSON>_shifted           3586 non-null   float64       \n", " 12  Último_shifted           3586 non-null   float64       \n", " 13  Volume_shifted           3586 non-null   float64       \n", " 14  MA_5_shifted             3586 non-null   float64       \n", " 15  MA_10_shifted            3581 non-null   float64       \n", " 16  MA_20_shifted            3571 non-null   float64       \n", " 17  MA_50_shifted            3541 non-null   float64       \n", " 18  BB_Upper_shifted         3571 non-null   float64       \n", " 19  BB_Lower_shifted         3571 non-null   float64       \n", " 20  BB_Width_shifted         3571 non-null   float64       \n", " 21  BB_Position_shifted      3571 non-null   float64       \n", " 22  RSI_shifted              3577 non-null   float64       \n", " 23  MACD_shifted             3586 non-null   float64       \n", " 24  Signal_Line_shifted      3586 non-null   float64       \n", " 25  atr_5_shifted            3586 non-null   float64       \n", " 26  atr_10_shifted           3581 non-null   float64       \n", " 27  atr_20_shifted           3571 non-null   float64       \n", " 28  volatility_5_shifted     3585 non-null   float64       \n", " 29  volatility_10_shifted    3580 non-null   float64       \n", " 30  volatility_20_shifted    3570 non-null   float64       \n", " 31  Price_Range_shifted      3586 non-null   float64       \n", " 32  Price_Position_shifted   3586 non-null   float64       \n", " 33  Gap_shifted              3586 non-null   float64       \n", " 34  hl_close_ratio_shifted   3586 non-null   float64       \n", " 35  true_range_shifted       3586 non-null   float64       \n", " 36  high_close_prev_shifted  3586 non-null   float64       \n", " 37  low_close_prev_shifted   3586 non-null   float64       \n", " 38  returns_shifted          3586 non-null   float64       \n", " 39  Variacao_shifted         3586 non-null   float64       \n", " 40  doji_prev                3586 non-null   int64         \n", " 41  hammer_prev              3586 non-null   int64         \n", " 42  shooting_star_prev       3586 non-null   int64         \n", " 43  engulfing_bullish_prev   3586 non-null   int64         \n", " 44  bullish_candle_prev      3586 non-null   int64         \n", " 45  bearish_candle_prev      3586 non-null   int64         \n", " 46  body_size_prev           3586 non-null   float64       \n", " 47  upper_shadow_prev        3586 non-null   float64       \n", " 48  lower_shadow_prev        3586 non-null   float64       \n", "dtypes: datetime64[ns](1), float64(34), int32(4), int64(10)\n", "memory usage: 1.3 MB\n", "\n", "📂 CATEGORIZAÇÃO DE FEATURES:\n", "   🏢 OHLC/Volume (0): []\n", "   📅 Temporais (8): ['Data', 'day_of_week', 'month', 'quarter', 'year', 'is_month_start', 'is_month_end', 'is_quarter_end']\n", "   📈 <PERSON><PERSON><PERSON><PERSON><PERSON> (18): ['<PERSON><PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted', 'MA_5_shifted', 'MA_10_shifted', 'MA_20_shifted']...\n", "   ⏰ Shifted (31): ['<PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON><PERSON>_shifted', '<PERSON><PERSON><PERSON>_shifted', 'Último_shifted', 'Volume_shifted']...\n", "   🕯️ Candlestick (11): ['high_close_prev_shifted', 'low_close_prev_shifted', 'doji_prev', 'hammer_prev', 'shooting_star_prev']...\n", "   🎯 Target (1): ['Target']\n", "   ❓ Outras (0): []\n", "\n", "🚨 ANÁLISE DE MUDANÇAS:\n", "   📉 Features PERDIDAS (0):\n", "      ✅ Nenhuma feature perdida\n", "   📈 Features GANHAS (0):\n", "      ➖ Nenhuma feature ganha\n", "   📊 Mudança líquida: +0 features\n", "\n", "⚠️ AVISO: <PERSON><PERSON> colunas que esperado\n", "   Esperado: >= 50\n", "   Atual: 49\n", "\n", "🔍 QUALIDADE DOS DADOS:\n", "   Colunas com valores ausentes: 13\n", "   Top 5 com mais NaNs:\n", "      MA_10_shifted: 5 (0.1%)\n", "      MA_20_shifted: 15 (0.4%)\n", "      MA_50_shifted: 45 (1.3%)\n", "      BB_Upper_shifted: 15 (0.4%)\n", "      BB_Lower_shifted: 15 (0.4%)\n", "\n", "✅ Auditoria de STEP 07 - Dataset Final concluída\n", "============================================================\n", "\n", "📋 RESUMO STEP 07:\n", "   ✅ Dataset final preparado\n", "   📊 Features finais: 49\n", "   🎯 Shape final: (3586, 49)\n", "   💾 Pronto para modelagem!\n"]}], "source": ["# STEP 07: <PERSON><PERSON><PERSON><PERSON> FINAL E AUDITORIA COMPLETA\n", "print(\"\\n📂 STEP 07: Preparando dataset final...\")\n", "\n", "# Criar dataset final\n", "df_step07_final = df_step06_candlestick.copy()\n", "\n", "# Limpeza final - remover linhas com muitos NaN\n", "print(f\"\\n🧹 Limpeza final de dados...\")\n", "print(f\"   Shape antes da limpeza: {df_step07_final.shape}\")\n", "\n", "# Contar NaN por linha\n", "nan_counts = df_step07_final.isnull().sum(axis=1)\n", "threshold = df_step07_final.shape[1] * 0.3  # Remover linhas com mais de 30% de NaN\n", "\n", "df_step07_final = df_step07_final[nan_counts <= threshold]\n", "print(f\"   <PERSON>hape ap<PERSON> limpeza: {df_step07_final.shape}\")\n", "print(f\"   Linhas removidas: {df_step06_candlestick.shape[0] - df_step07_final.shape[0]}\")\n", "\n", "# Auditoria final\n", "audit_step07 = audit_pipeline_step(\n", "    df_step07_final, \n", "    \"STEP 07 - Dataset Final\", \n", "    expected_min_cols=50,  # Esperamos pelo menos 50 features\n", "    previous_df=df_step06_candlestick\n", ")\n", "\n", "print(f\"\\n📋 RESUMO STEP 07:\")\n", "print(f\"   ✅ Dataset final preparado\")\n", "print(f\"   📊 Features finais: {df_step07_final.shape[1]}\")\n", "print(f\"   🎯 Shape final: {df_step07_final.shape}\")\n", "print(f\"   💾 Pronto para modelagem!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PREPARAÇÃO DOS DADOS PARA MODELAGEM"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset final - Train: (2868, 47), Test: (718, 47)\n", "NaN removidos com forward fill\n"]}], "source": ["# Preparar splits temporais usando o dataset final\n", "def create_temporal_split(df, test_size=0.2):\n", "    df_sorted = df.sort_values('Data').reset_index(drop=True) if 'Data' in df.columns else df.copy()\n", "    split_idx = int(len(df_sorted) * (1 - test_size))\n", "    \n", "    train_df = df_sorted.iloc[:split_idx]\n", "    test_df = df_sorted.iloc[split_idx:]\n", "    \n", "    feature_cols = [col for col in df_sorted.columns if col not in ['Data', 'Target']]\n", "    \n", "    X_train = train_df[feature_cols]\n", "    X_test = test_df[feature_cols]\n", "    y_train = train_df['Target']\n", "    y_test = test_df['Target']\n", "    \n", "    return X_train, X_test, y_train, y_test\n", "\n", "# Usar o dataset final completo\n", "X_train_final, X_test_final, y_train_final, y_test_final = create_temporal_split(df_step07_final)\n", "\n", "# Remover NaN com forward fill\n", "X_train_final = X_train_final.fillna(method='ffill').fillna(0)\n", "X_test_final = X_test_final.fillna(method='ffill').fillna(0)\n", "\n", "print(f\"Dataset final - Train: {X_train_final.shape}, Test: {X_test_final.shape}\")\n", "print(f\"NaN removidos com forward fill\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Modelos preparados: ['Logistic Regression', 'Random Forest', 'XGBoost', 'LightGBM', 'SVM']\n"]}], "source": ["# Definir modelos\n", "models = {\n", "    'Logistic Regression': Pipeline([\n", "        ('scaler', StandardScaler()),\n", "        ('logreg', LogisticRegression(C=0.1, solver='liblinear', random_state=42))\n", "    ]),\n", "    \n", "    'Random Forest': RandomForestClassifier(\n", "        n_estimators=100, max_depth=10, min_samples_split=5,\n", "        min_samples_leaf=2, random_state=42, n_jobs=-1\n", "    ),\n", "    \n", "    'XGBoost': xgb.XGBClassifier(\n", "        n_estimators=100, max_depth=6, learning_rate=0.1,\n", "        subsample=0.8, colsample_bytree=0.8, random_state=42, eval_metric='logloss'\n", "    ),\n", "    \n", "    'LightGBM': lgb.LGBMClassifier(\n", "        n_estimators=100, max_depth=6, learning_rate=0.1,\n", "        subsample=0.8, colsample_bytree=0.8, random_state=42, verbose=-1\n", "    ),\n", "    \n", "    'SVM': Pipeline([\n", "        ('scaler', StandardScaler()),\n", "        ('svm', SVC(kernel='rbf', probability=True, random_state=42, C=1.0))\n", "    ])\n", "}\n", "\n", "print(f\"Modelos preparados: {list(models.keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## TREINAMENTO DOS MODELOS COM DATASET COMPLETO"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 INICIANDO TREINAMENTO COM DATASET COMPLETO\n", "📊 Dataset: (2868, 47) features\n", "\n", "Treinando Logistic Regression...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["   ✅ Concluído em 0.06s\n", "\n", "Treinando Random Forest...\n", "   ✅ Concluído em 0.25s\n", "\n", "Treinando XGBoost...\n", "   ✅ Concluído em 0.23s\n", "\n", "Treinando LightGBM...\n", "   ✅ Conclu<PERSON>do em 1.62s\n", "\n", "Treinando SVM...\n", "   ✅ Conclu<PERSON>do em 1.93s\n", "\n", "🎯 TREINAMENTO CONCLUÍDO!\n", "   ✅ 5 modelos treinados com sucesso\n", "   📊 Dataset com 47 features (incluindo candlestick)\n", "   ⏰ Tempo total: 4.09s\n"]}], "source": ["# Treinar modelos com o dataset final completo\n", "print(\"🚀 INICIANDO TREINAMENTO COM DATASET COMPLETO\")\n", "print(f\"📊 Dataset: {X_train_final.shape} features\")\n", "\n", "trained_models_final = {}\n", "training_times = {}\n", "total_start_time = time.time()\n", "\n", "for name, model in models.items():\n", "    print(f\"\\nTreinando {name}...\")\n", "    start_time = time.time()\n", "    \n", "    # Criar nova instância do modelo\n", "    if hasattr(model, 'get_params'):\n", "        if isinstance(model, Pipeline):\n", "            # Para pipelines, criar nova instância\n", "            from sklearn.base import clone\n", "            model_instance = clone(model)\n", "        else:\n", "            # Para modelos simples\n", "            model_instance = model.__class__(**model.get_params())\n", "    else:\n", "        model_instance = model\n", "    \n", "    # Treinar modelo\n", "    model_instance.fit(X_train_final, y_train_final)\n", "    \n", "    end_time = time.time()\n", "    training_time = end_time - start_time\n", "    training_times[name] = training_time\n", "    \n", "    trained_models_final[name] = model_instance\n", "    print(f\"   ✅ Conc<PERSON>ído em {training_time:.2f}s\")\n", "\n", "total_time = time.time() - total_start_time\n", "\n", "print(f\"\\n🎯 TREINAMENTO CONCLUÍDO!\")\n", "print(f\"   ✅ {len(trained_models_final)} modelos treinados com sucesso\")\n", "print(f\"   📊 Dataset com {X_train_final.shape[1]} features (incluindo candlestick)\")\n", "print(f\"   ⏰ Tempo total: {total_time:.2f}s\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Função de avaliação preparada\n"]}], "source": ["# Função de avaliação\n", "def evaluate_model(model, X_train, X_test, y_train, y_test, model_name, dataset_name):\n", "    start_time = time.time()\n", "    \n", "    model.fit(X_train, y_train)\n", "    y_pred = model.predict(X_test)\n", "    y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None\n", "    \n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    auc_score = roc_auc_score(y_test, y_pred_proba) if y_pred_proba is not None else None\n", "    training_time = time.time() - start_time\n", "    \n", "    tscv = TimeSeriesSplit(n_splits=5)\n", "    cv_scores = cross_val_score(model, X_train, y_train, cv=tscv, scoring='accuracy')\n", "    \n", "    return {\n", "        'model_name': model_name,\n", "        'dataset': dataset_name,\n", "        'accuracy': accuracy,\n", "        'auc_score': auc_score,\n", "        'cv_mean': cv_scores.mean(),\n", "        'cv_std': cv_scores.std(),\n", "        'training_time': training_time,\n", "        'model': model\n", "    }\n", "\n", "print(\"Função de avaliação preparada\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "<PERSON><PERSON>o Lo<PERSON> Regression...\n", "  Dataset A: Error - name 'X_train_a' is not defined\n", "  Dataset B: Error - name 'X_train_b' is not defined\n", "\n", "Testando Random Forest...\n", "  Dataset A: Error - name 'X_train_a' is not defined\n", "  Dataset B: Error - name 'X_train_b' is not defined\n", "\n", "Testando XGBoost...\n", "  Dataset A: Error - name 'X_train_a' is not defined\n", "  Dataset B: Error - name 'X_train_b' is not defined\n", "\n", "Testando LightGBM...\n", "  Dataset A: Error - name 'X_train_a' is not defined\n", "  Dataset B: Error - name 'X_train_b' is not defined\n", "\n", "Testando SVM...\n", "  Dataset A: Error - name 'X_train_a' is not defined\n", "  Dataset B: Error - name 'X_train_b' is not defined\n", "\n", "Resultados coletados: 0\n"]}], "source": ["# Executar olimpíada\n", "results = []\n", "\n", "for model_name, model in models.items():\n", "    print(f\"\\nTestando {model_name}...\")\n", "    \n", "    # Dataset A\n", "    try:\n", "        result_a = evaluate_model(model, X_train_a, X_test_a, y_train_a, y_test_a, \n", "                                model_name, \"Dataset A\")\n", "        results.append(result_a)\n", "        auc_str_a = f'{result_a[\"auc_score\"]:.4f}' if result_a['auc_score'] else 'N/A'\n", "        print(f'  Dataset A: Acc={result_a[\"accuracy\"]:.4f}, AUC={auc_str_a}')\n", "\n", "    except Exception as e:\n", "        print(f\"  Dataset A: Error - {e}\")\n", "    \n", "    # Dataset B\n", "    try:\n", "        result_b = evaluate_model(model, X_train_b, X_test_b, y_train_b, y_test_b, \n", "                                model_name, \"Dataset B\")\n", "        results.append(result_b)\n", "        auc_str_b = f'{result_b[\"auc_score\"]:.4f}' if result_b['auc_score'] else 'N/A'\n", "        print(f'  Dataset B: Acc={result_b[\"accuracy\"]:.4f}, AUC={auc_str_b}')\n", "\n", "        \n", "        if len(results) >= 2:\n", "            diff = result_b['accuracy'] - result_a['accuracy']\n", "            print(f\"  Diferença: {diff:+.4f}\")\n", "    except Exception as e:\n", "        print(f\"  Dataset B: Error - {e}\")\n", "\n", "print(f\"\\nResultados coletados: {len(results)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## AVALIAÇÃO COM DATASET FINAL COMPLETO"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 AVALIANDO MODELOS COM DATASET COMPLETO\n", "📊 Dataset de teste: (718, 47) features\n", "\n", "<PERSON><PERSON><PERSON> Logistic Regression...\n", "   ✅ Accuracy: 0.4986 | AUC: 0.5142 | CV: 0.4904±0.0288\n", "\n", "Avaliando Random Forest...\n", "   ✅ Accuracy: 0.5056 | AUC: 0.5399 | CV: 0.4975±0.0206\n", "\n", "Avaliando XGBoost...\n", "   ✅ Accuracy: 0.5432 | AUC: 0.5663 | CV: 0.5075±0.0143\n", "\n", "Avaliando LightGBM...\n", "   ✅ Accuracy: 0.5404 | AUC: 0.5459 | CV: 0.5092±0.0165\n", "\n", "Avaliando SVM...\n", "   ✅ Accuracy: 0.5348 | AUC: 0.4476 | CV: 0.4795±0.0270\n", "\n", "🏆 RESULTADOS FINAIS:\n", "   🥇 Melhor modelo: XGBoost (Acc: 0.5432)\n", "   📊 Todos os modelos avaliados com dataset completo de 47 features\n", "   🕯️ Incluindo padrões de candlestick e features temporais\n"]}], "source": ["# Avaliar modelos com dataset final completo\n", "print(\"🎯 AVALIANDO MODELOS COM DATASET COMPLETO\")\n", "print(f\"📊 Dataset de teste: {X_test_final.shape} features\")\n", "\n", "results_final = []\n", "best_accuracy = 0\n", "best_model_name = \"\"\n", "\n", "for name, model in trained_models_final.items():\n", "    print(f\"\\nAvaliando {name}...\")\n", "    \n", "    # Fazer predições\n", "    y_pred = model.predict(X_test_final)\n", "    y_pred_proba = model.predict_proba(X_test_final)[:, 1] if hasattr(model, 'predict_proba') else None\n", "    \n", "    # Calcular métricas\n", "    accuracy = accuracy_score(y_test_final, y_pred)\n", "    auc_score = roc_auc_score(y_test_final, y_pred_proba) if y_pred_proba is not None else None\n", "    \n", "    # Cross-validation\n", "    tscv = TimeSeriesSplit(n_splits=5)\n", "    cv_scores = cross_val_score(model, X_train_final, y_train_final, cv=tscv, scoring='accuracy')\n", "    \n", "    result = {\n", "        'model_name': name,\n", "        'dataset': \"Dataset Final Completo\",\n", "        'accuracy': accuracy,\n", "        'auc_score': auc_score,\n", "        'cv_mean': cv_scores.mean(),\n", "        'cv_std': cv_scores.std(),\n", "        'model': model\n", "    }\n", "    \n", "    results_final.append(result)\n", "    \n", "    # Acompanhar melhor modelo\n", "    if accuracy > best_accuracy:\n", "        best_accuracy = accuracy\n", "        best_model_name = name\n", "    \n", "    print(f\"   ✅ Accuracy: {accuracy:.4f} | AUC: {auc_score:.4f} | CV: {cv_scores.mean():.4f}±{cv_scores.std():.4f}\")\n", "\n", "print(f\"\\n🏆 RESULTADOS FINAIS:\")\n", "print(f\"   🥇 Melhor modelo: {best_model_name} (Acc: {best_accuracy:.4f})\")\n", "print(f\"   📊 Todos os modelos avaliados com dataset completo de {X_test_final.shape[1]} features\")\n", "print(f\"   🕯️ Incluindo padrões de candlestick e features temporais\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 RESUMO FINAL DO PIPELINE EXPLÍCITO\n", "\n", "### ✅ CORREÇÕES IMPLEMENTADAS:\n", "\n", "1. **PIPELINE EXPLÍCITO DE 7 STEPS**: Cada etapa com nome único e auditoria completa\n", "2. **FEATURES COMPLETAS**: Todas as features técnicas, temporais e candlestick preservadas\n", "3. **AUDITORIA RIGOROSA**: Função de rastreamento em cada step para identificar \"penhascos\"\n", "4. **CORREÇÃO TEMPORAL**: Aplicação correta de shift para evitar data leakage\n", "5. **DATASET FINAL**: 47 features (vs. 40 anteriores) com padrões de candlestick\n", "\n", "### 📊 PIPELINE FINAL:\n", "- **STEP 01**: <PERSON><PERSON> (3592, 7)\n", "- **STEP 02**: Processamento básico (3591, 10)\n", "- **STEP 03**: Features técnicas (3591, 44)\n", "- **STEP 04**: Seleção conservadora (3591, 40)\n", "- **STEP 05**: Correção temporal (3590, 40)\n", "- **STEP 06**: <PERSON><PERSON><PERSON><PERSON> candlestick (3590, 49)\n", "- **STEP 07**: Dataset final (3586, 49)\n", "\n", "### 🏆 RESULTADOS:\n", "- **Dataset completo**: 47 features para modelagem\n", "- **Melhor modelo**: XGBoost com accuracy de ~53.5%\n", "- **Features preservadas**: Todas as features importantes mantidas\n", "- **Pipeline auditado**: Cada step rastreado e documentado\n", "\n", "### 🔧 PRÓXIMOS PASSOS SUGERIDOS:\n", "1. **Otimização de hiperparâmetros** dos modelos\n", "2. **Feature engineering adicional** baseado nos resultados\n", "3. **Ensemble methods** combinando os melhores modelos\n", "4. **<PERSON><PERSON><PERSON><PERSON> de importância** das features candlestick"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nenhum resultado coletado\n"]}], "source": ["# <PERSON><PERSON><PERSON><PERSON> dos resultados\n", "if results:\n", "    results_df = pd.DataFrame(results)\n", "    \n", "    print(\"\\nTabela de Resultados:\")\n", "    print(f\"{'Modelo':<20} {'Dataset':<12} {'Accuracy':<10} {'AUC':<8} {'CV Mean':<10} {'Tempo':<8}\")\n", "    print(\"-\" * 75)\n", "    \n", "    for _, row in results_df.iterrows():\n", "        auc_str = f\"{row['auc_score']:.4f}\" if row['auc_score'] is not None else \"N/A\"\n", "        print(f\"{row['model_name']:<20} {row['dataset']:<12} {row['accuracy']:<10.4f} {auc_str:<8} {row['cv_mean']:<10.4f} {row['training_time']:<8.2f}\")\n", "    \n", "    # Ranking por dataset\n", "    print(\"\\nRanking Dataset A:\")\n", "    dataset_a_results = results_df[results_df['dataset'] == 'Dataset A'].sort_values('accuracy', ascending=False)\n", "    for i, (_, row) in enumerate(dataset_a_results.iterrows(), 1):\n", "        print(f\"  {i}. {row['model_name']}: {row['accuracy']:.4f}\")\n", "    \n", "    print(\"\\nRanking Dataset B:\")\n", "    dataset_b_results = results_df[results_df['dataset'] == 'Dataset B'].sort_values('accuracy', ascending=False)\n", "    for i, (_, row) in enumerate(dataset_b_results.iterrows(), 1):\n", "        print(f\"  {i}. {row['model_name']}: {row['accuracy']:.4f}\")\n", "    \n", "    # Impact<PERSON> das features de candlestick\n", "    print(\"\\nImpacto das Features de Candlestick:\")\n", "    candlestick_impact = []\n", "    \n", "    for model_name in results_df['model_name'].unique():\n", "        model_results = results_df[results_df['model_name'] == model_name]\n", "        if len(model_results) == 2:\n", "            baseline = model_results[model_results['dataset'] == 'Dataset A'].iloc[0]\n", "            enriched = model_results[model_results['dataset'] == 'Dataset B'].iloc[0]\n", "            \n", "            improvement = enriched['accuracy'] - baseline['accuracy']\n", "            candlestick_impact.append(improvement)\n", "            \n", "            print(f\"  {model_name}: {baseline['accuracy']:.4f} -> {enriched['accuracy']:.4f} ({improvement:+.4f})\")\n", "    \n", "    if candlestick_impact:\n", "        avg_improvement = np.mean(candlestick_impact)\n", "        positive_count = sum(1 for x in candlestick_impact if x > 0)\n", "        \n", "        print(f\"\\nResumo do Impacto:\")\n", "        print(f\"  Melhoria média: {avg_improvement:+.4f}\")\n", "        print(f\"  Modelos que melhoraram: {positive_count}/{len(candlestick_impact)}\")\n", "        \n", "        if avg_improvement > 0.01:\n", "            print(f\"  Conclusão: Features de candlestick agregam valor significativo\")\n", "        elif avg_improvement > 0:\n", "            print(f\"  Conclusão: Features de candlestick agregam valor marginal\")\n", "        else:\n", "            print(f\"  Conclusão: Features de candlestick não agregam valor\")\n", "    \n", "    # Campeão geral\n", "    best_result = results_df.loc[results_df['accuracy'].idxmax()]\n", "    print(f\"\\nCampeão Geral:\")\n", "    print(f\"  Modelo: {best_result['model_name']}\")\n", "    print(f\"  Dataset: {best_result['dataset']}\")\n", "    print(f\"  Accuracy: {best_result['accuracy']:.4f}\")\n", "    print(f\"  AUC: {best_result['auc_score']:.4f}\" if best_result['auc_score'] else \"  AUC: N/A\")\n", "\n", "else:\n", "    print(\"Nenhum resultado coletado\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Olimpíada concluída\n"]}], "source": ["# Ensemble dos melhores\n", "if len(results) >= 6:  # Pelo menos 3 modelos testados em ambos datasets\n", "    dataset_b_results = [r for r in results if r['dataset'] == 'Dataset B']\n", "    \n", "    if len(dataset_b_results) >= 3:\n", "        dataset_b_results.sort(key=lambda x: x['accuracy'], reverse=True)\n", "        top_3 = dataset_b_results[:3]\n", "        \n", "        print(f\"\\nTop 3 para ensemble: {[r['model_name'] for r in top_3]}\")\n", "        \n", "        ensemble_estimators = []\n", "        for i, result in enumerate(top_3):\n", "            name = f\"model_{i+1}\"\n", "            ensemble_estimators.append((name, result['model']))\n", "        \n", "        voting_classifier = VotingClassifier(estimators=ensemble_estimators, voting='soft')\n", "        \n", "        try:\n", "            ensemble_result = evaluate_model(\n", "                voting_classifier, X_train_b, X_test_b, y_train_b, y_test_b,\n", "                \"Voting Classifier\", \"Dataset B\"\n", "            )\n", "            \n", "            print(f\"\\nEnsemble Result:\")\n", "            print(f\"  Accuracy: {ensemble_result['accuracy']:.4f}\")\n", "            print(f\"  AUC: {ensemble_result['auc_score']:.4f}\")\n", "            \n", "            best_individual = top_3[0]\n", "            improvement = ensemble_result['accuracy'] - best_individual['accuracy']\n", "            print(f\"  vs Best Individual: {improvement:+.4f}\")\n", "            \n", "            results.append(ensemble_result)\n", "            \n", "        except Exception as e:\n", "            print(f\"Ensemble error: {e}\")\n", "\n", "print(\"\\nOlimpíada concluída\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Análise visual concluída\n"]}], "source": ["# Visualização simples\n", "if results:\n", "    results_df = pd.DataFrame(results)\n", "    \n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # Gráfico de accuracy por modelo e dataset\n", "    plt.subplot(2, 2, 1)\n", "    models_list = results_df['model_name'].unique()\n", "    datasets_list = results_df['dataset'].unique()\n", "    \n", "    x = np.arange(len(models_list))\n", "    width = 0.35\n", "    \n", "    for i, dataset in enumerate(datasets_list):\n", "        dataset_data = results_df[results_df['dataset'] == dataset]\n", "        accuracies = []\n", "        \n", "        for model in models_list:\n", "            model_data = dataset_data[dataset_data['model_name'] == model]\n", "            if len(model_data) > 0:\n", "                accuracies.append(model_data.iloc[0]['accuracy'])\n", "            else:\n", "                accuracies.append(0)\n", "        \n", "        plt.bar(x + i*width, accuracies, width, label=dataset, alpha=0.8)\n", "    \n", "    plt.xlabel('Modelos')\n", "    plt.ylabel('Accuracy')\n", "    plt.title('Accuracy por Modelo e Dataset')\n", "    plt.xticks(x + width/2, models_list, rotation=45, ha='right')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Tempo vs Accuracy\n", "    plt.subplot(2, 2, 2)\n", "    plt.scatter([r['training_time'] for r in results], \n", "               [r['accuracy'] for r in results], alpha=0.7)\n", "    plt.xlabel('Tempo de Treinamento (s)')\n", "    plt.ylabel('Accuracy')\n", "    plt.title('Accuracy vs Tempo')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Distribuição de AUC\n", "    plt.subplot(2, 2, 3)\n", "    auc_scores = [r['auc_score'] for r in results if r['auc_score'] is not None]\n", "    if auc_scores:\n", "        plt.hist(auc_scores, bins=10, alpha=0.7, edgecolor='black')\n", "        plt.xlabel('AUC Score')\n", "        plt.ylabel('Frequência')\n", "        plt.title('Distribuição AUC')\n", "        plt.grid(True, alpha=0.3)\n", "    \n", "    # Impacto candlestick\n", "    plt.subplot(2, 2, 4)\n", "    if 'candlestick_impact' in locals() and candlestick_impact:\n", "        models_impact = [results_df[results_df['model_name'] == model]['model_name'].iloc[0] \n", "                        for model in results_df['model_name'].unique() \n", "                        if len(results_df[results_df['model_name'] == model]) == 2]\n", "        \n", "        if len(models_impact) == len(candlestick_impact):\n", "            colors = ['green' if x > 0 else 'red' for x in candlestick_impact]\n", "            plt.bar(models_impact, candlestick_impact, color=colors, alpha=0.7)\n", "            plt.xlabel('Modelos')\n", "            plt.ylabel('Melhoria Accuracy')\n", "            plt.title('Impacto Features Candlestick')\n", "            plt.xticks(rotation=45, ha='right')\n", "            plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "            plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "print(\"Análise visual concluída\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.4"}}, "nbformat": 4, "nbformat_minor": 4}