import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, roc_auc_score
import xgboost as xgb
import lightgbm as lgb
import matplotlib.pyplot as plt
import time

# FUNÇÃO DE AUDITORIA RIGOROSA
def audit_pipeline_step(df, step_name, expected_min_cols=None, previous_df=None):
    """
    Auditoria rigorosa de cada etapa do pipeline
    Identifica exatamente onde features são perdidas
    """
    print(f"\n{'='*60}")
    print(f"🔍 AUDITORIA: {step_name.upper()}")
    print(f"{'='*60}")
    
    # Informações básicas
    print(f"📊 INFORMAÇÕES BÁSICAS:")
    print(f"   Shape: {df.shape}")
    print(f"   Memória: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    
    # Usar .info() para inspeção detalhada
    print(f"\n📋 DETALHES DAS COLUNAS (.info()):")
    df.info()
    
    # Categorização de features
    ohlc_cols = [col for col in df.columns if col in ['Abertura', 'Máxima', 'Mínima', 'Último', 'Volume']]
    temporal_cols = [col for col in df.columns if any(x in col.lower() for x in ['data', 'day', 'month', 'quarter', 'year'])]
    technical_cols = [col for col in df.columns if any(x in col.lower() for x in ['ma_', 'bb_', 'rsi', 'atr', 'volatility', 'macd'])]
    shifted_cols = [col for col in df.columns if col.endswith('_shifted')]
    candlestick_cols = [col for col in df.columns if any(x in col.lower() for x in ['doji', 'hammer', 'engulf', 'marubozu', 'shooting', '_prev'])]
    target_cols = [col for col in df.columns if 'target' in col.lower()]
    other_cols = [col for col in df.columns if col not in ohlc_cols + temporal_cols + technical_cols + shifted_cols + candlestick_cols + target_cols]
    
    print(f"\n📂 CATEGORIZAÇÃO DE FEATURES:")
    print(f"   🏢 OHLC/Volume ({len(ohlc_cols)}): {ohlc_cols}")
    print(f"   📅 Temporais ({len(temporal_cols)}): {temporal_cols}")
    print(f"   📈 Técnicas ({len(technical_cols)}): {technical_cols[:5]}{'...' if len(technical_cols) > 5 else ''}")
    print(f"   ⏰ Shifted ({len(shifted_cols)}): {shifted_cols[:5]}{'...' if len(shifted_cols) > 5 else ''}")
    print(f"   🕯️ Candlestick ({len(candlestick_cols)}): {candlestick_cols[:5]}{'...' if len(candlestick_cols) > 5 else ''}")
    print(f"   🎯 Target ({len(target_cols)}): {target_cols}")
    print(f"   ❓ Outras ({len(other_cols)}): {other_cols[:5]}{'...' if len(other_cols) > 5 else ''}")
    
    # Verificar se há perda de features
    if previous_df is not None:
        print(f"\n🚨 ANÁLISE DE MUDANÇAS:")
        prev_cols = set(previous_df.columns)
        curr_cols = set(df.columns)
        
        lost_features = prev_cols - curr_cols
        gained_features = curr_cols - prev_cols
        
        print(f"   📉 Features PERDIDAS ({len(lost_features)}):")
        if lost_features:
            for feature in sorted(lost_features):
                print(f"      ❌ {feature}")
        else:
            print(f"      ✅ Nenhuma feature perdida")
        
        print(f"   📈 Features GANHAS ({len(gained_features)}):")
        if gained_features:
            for feature in sorted(gained_features):
                print(f"      ✅ {feature}")
        else:
            print(f"      ➖ Nenhuma feature ganha")
        
        net_change = len(curr_cols) - len(prev_cols)
        print(f"   📊 Mudança líquida: {net_change:+d} features")
        
        # ALERTA para perdas significativas
        if len(lost_features) > 5:
            print(f"\n🚨 ALERTA: PERDA SIGNIFICATIVA DE FEATURES!")
            print(f"   {len(lost_features)} features foram perdidas nesta etapa")
            print(f"   Isso pode indicar um 'PENHASCO' no pipeline")
    
    # Verificar expectativas
    if expected_min_cols and len(df.columns) < expected_min_cols:
        print(f"\n⚠️ AVISO: Menos colunas que esperado")
        print(f"   Esperado: >= {expected_min_cols}")
        print(f"   Atual: {len(df.columns)}")
    
    # Verificar qualidade dos dados
    print(f"\n🔍 QUALIDADE DOS DADOS:")
    null_counts = df.isnull().sum()
    cols_with_nulls = null_counts[null_counts > 0]
    print(f"   Colunas com valores ausentes: {len(cols_with_nulls)}")
    if len(cols_with_nulls) > 0:
        print(f"   Top 5 com mais NaNs:")
        for col, count in cols_with_nulls.head().items():
            print(f"      {col}: {count} ({count/len(df):.1%})")
    
    print(f"\n✅ Auditoria de {step_name} concluída")
    print(f"{'='*60}")
    
    return {
        'step_name': step_name,
        'shape': df.shape,
        'columns': list(df.columns),
        'ohlc_count': len(ohlc_cols),
        'technical_count': len(technical_cols),
        'shifted_count': len(shifted_cols),
        'candlestick_count': len(candlestick_cols)
    }

print("✅ Função de auditoria rigorosa criada")
print("📋 Pronta para rastrear cada etapa do pipeline")

# STEP 01: CARREGAMENTO DOS DADOS BRUTOS
print("🚀 INICIANDO PIPELINE EXPLÍCITO")
print("📂 STEP 01: Carregando dados brutos...")

# Carregar dados brutos
df_step01_raw = pd.read_csv('Dados Históricos - Ibovespa.csv', encoding='utf-8')

# Auditoria do carregamento
audit_step01 = audit_pipeline_step(df_step01_raw, "STEP 01 - Dados Brutos")

print(f"\n📋 RESUMO STEP 01:")
print(f"   ✅ Dados carregados: {df_step01_raw.shape}")
print(f"   📊 Colunas originais: {list(df_step01_raw.columns)}")

# STEP 02: PROCESSAMENTO BÁSICO
print("\n📂 STEP 02: Processamento básico...")

# Criar cópia para processamento
df_step02_processed = df_step01_raw.copy()

# Processamento de data
df_step02_processed['Data'] = pd.to_datetime(df_step02_processed['Data'], format='%d.%m.%Y')
df_step02_processed = df_step02_processed.sort_values('Data').reset_index(drop=True)

# Tratar valores ausentes
df_step02_processed['Vol.'] = df_step02_processed['Vol.'].fillna(method='ffill')

# Converter Volume para numérico
def converter_volume(vol_str):
    if pd.isna(vol_str): return np.nan
    vol_str = str(vol_str).replace(',', '.')
    if 'B' in vol_str: return float(vol_str.replace('B', '')) * 1e9
    elif 'M' in vol_str: return float(vol_str.replace('M', '')) * 1e6
    elif 'K' in vol_str: return float(vol_str.replace('K', '')) * 1e3
    return float(vol_str)

df_step02_processed['Volume'] = df_step02_processed['Vol.'].apply(converter_volume)
df_step02_processed['Variacao'] = df_step02_processed['Var%'].str.replace('%', '').str.replace(',', '.').astype(float) / 100

# Criar target
df_step02_processed['Target'] = (df_step02_processed['Variacao'].shift(-1) > 0).astype(int)
df_step02_processed = df_step02_processed[:-1].copy()  # Remove última linha

# Auditoria do processamento
audit_step02 = audit_pipeline_step(
    df_step02_processed, 
    "STEP 02 - Processamento Básico", 
    expected_min_cols=len(df_step01_raw.columns),
    previous_df=df_step01_raw
)

print(f"\n📋 RESUMO STEP 02:")
print(f"   ✅ Target criado corretamente")
print(f"   📊 Shape após processamento: {df_step02_processed.shape}")
print(f"   🎯 Distribuição do target:")
print(df_step02_processed['Target'].value_counts(normalize=True))

# STEP 03: CRIAÇÃO DE FEATURES TÉCNICAS
print("\n📂 STEP 03: Criando features técnicas...")

# Criar cópia para features
df_step03_features = df_step02_processed.copy()

print("📈 Criando indicadores técnicos...")

# Médias móveis
for periodo in [5, 10, 20, 50]:
    df_step03_features[f'MA_{periodo}'] = df_step03_features['Último'].rolling(window=periodo).mean()

# Bandas de Bollinger
df_step03_features['BB_Middle'] = df_step03_features['Último'].rolling(window=20).mean()
bb_std = df_step03_features['Último'].rolling(window=20).std()
df_step03_features['BB_Upper'] = df_step03_features['BB_Middle'] + (bb_std * 2)
df_step03_features['BB_Lower'] = df_step03_features['BB_Middle'] - (bb_std * 2)
df_step03_features['BB_Width'] = df_step03_features['BB_Upper'] - df_step03_features['BB_Lower']
df_step03_features['BB_Position'] = (df_step03_features['Último'] - df_step03_features['BB_Lower']) / df_step03_features['BB_Width']

# RSI
def calculate_rsi(prices, window=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

df_step03_features['RSI'] = calculate_rsi(df_step03_features['Último'])

# MACD
ema_12 = df_step03_features['Último'].ewm(span=12).mean()
ema_26 = df_step03_features['Último'].ewm(span=26).mean()
df_step03_features['MACD'] = ema_12 - ema_26
df_step03_features['Signal_Line'] = df_step03_features['MACD'].ewm(span=9).mean()

# ATR (Average True Range)
df_step03_features['high_low'] = df_step03_features['Máxima'] - df_step03_features['Mínima']
df_step03_features['high_close_prev'] = abs(df_step03_features['Máxima'] - df_step03_features['Último'].shift(1))
df_step03_features['low_close_prev'] = abs(df_step03_features['Mínima'] - df_step03_features['Último'].shift(1))
df_step03_features['true_range'] = df_step03_features[['high_low', 'high_close_prev', 'low_close_prev']].max(axis=1)

for periodo in [5, 10, 20]:
    df_step03_features[f'atr_{periodo}'] = df_step03_features['true_range'].rolling(window=periodo).mean()

# Volatilidade
df_step03_features['returns'] = df_step03_features['Último'].pct_change()
for periodo in [5, 10, 20]:
    df_step03_features[f'volatility_{periodo}'] = df_step03_features['returns'].rolling(window=periodo).std()

# Features de preço
df_step03_features['Price_Range'] = df_step03_features['Máxima'] - df_step03_features['Mínima']
df_step03_features['Price_Position'] = (df_step03_features['Último'] - df_step03_features['Mínima']) / df_step03_features['Price_Range']
df_step03_features['Gap'] = df_step03_features['Abertura'] - df_step03_features['Último'].shift(1)
df_step03_features['hl_close_ratio'] = (df_step03_features['Máxima'] - df_step03_features['Mínima']) / df_step03_features['Último']

# Features temporais
df_step03_features['day_of_week'] = df_step03_features['Data'].dt.dayofweek
df_step03_features['month'] = df_step03_features['Data'].dt.month
df_step03_features['quarter'] = df_step03_features['Data'].dt.quarter
df_step03_features['year'] = df_step03_features['Data'].dt.year
df_step03_features['is_month_start'] = (df_step03_features['Data'].dt.day <= 5).astype(int)
df_step03_features['is_month_end'] = (df_step03_features['Data'].dt.day >= 25).astype(int)
df_step03_features['is_quarter_end'] = df_step03_features['Data'].dt.is_quarter_end.astype(int)

# Auditoria da criação de features
audit_step03 = audit_pipeline_step(
    df_step03_features, 
    "STEP 03 - Features Técnicas", 
    expected_min_cols=30,  # Esperamos pelo menos 30 features
    previous_df=df_step02_processed
)

print(f"\n📋 RESUMO STEP 03:")
print(f"   ✅ Features técnicas criadas: {df_step03_features.shape[1] - df_step02_processed.shape[1]}")
print(f"   📊 Total de features: {df_step03_features.shape[1]}")
print(f"   🎯 Shape final: {df_step03_features.shape}")

# STEP 04: SELEÇÃO DE FEATURES - PONTO CRÍTICO!
print("\n📂 STEP 04: Seleção de features (PONTO CRÍTICO)...")
print("🚨 ATENÇÃO: Esta é uma área comum para 'PENHASCOS' de features!")

# ANTES da seleção - checkpoint crítico
print(f"\n🔍 CHECKPOINT ANTES DA SELEÇÃO:")
print(f"   Features disponíveis: {df_step03_features.shape[1]}")
print(f"   Primeiras 10: {list(df_step03_features.columns)[:10]}")
print(f"   Últimas 10: {list(df_step03_features.columns)[-10:]}")

# Criar cópia para seleção
df_step04_selected = df_step03_features.copy()

# ESTRATÉGIA CONSERVADORA: Manter TODAS as features criadas
# Apenas remover colunas auxiliares e duplicadas
columns_to_remove = [
    'Vol.',  # Versão string do volume
    'Var%',  # Versão string da variação
    'high_low',  # Auxiliar para ATR
    'BB_Middle'  # Duplicata da MA_20
]

# Remover apenas colunas auxiliares
columns_to_remove_existing = [col for col in columns_to_remove if col in df_step04_selected.columns]
if columns_to_remove_existing:
    print(f"\n🗑️ Removendo apenas colunas auxiliares: {columns_to_remove_existing}")
    df_step04_selected = df_step04_selected.drop(columns=columns_to_remove_existing)
else:
    print(f"\n✅ Nenhuma coluna auxiliar para remover")

# CHECKPOINT CRÍTICO: Verificar se não perdemos features importantes
features_importantes = [
    'Abertura', 'Máxima', 'Mínima', 'Último', 'Volume',
    'MA_5', 'MA_10', 'MA_20', 'MA_50',
    'BB_Upper', 'BB_Lower', 'BB_Width', 'BB_Position',
    'RSI', 'MACD', 'Signal_Line',
    'atr_5', 'atr_10', 'atr_20',
    'volatility_5', 'volatility_10', 'volatility_20',
    'Price_Range', 'Price_Position', 'Gap', 'hl_close_ratio',
    'day_of_week', 'month', 'quarter', 'year',
    'is_month_start', 'is_month_end', 'is_quarter_end',
    'Target'
]

features_perdidas = [f for f in features_importantes if f not in df_step04_selected.columns]
if features_perdidas:
    print(f"\n🚨 ALERTA: Features importantes perdidas: {features_perdidas}")
else:
    print(f"\n✅ Todas as features importantes preservadas")

# Auditoria da seleção
audit_step04 = audit_pipeline_step(
    df_step04_selected, 
    "STEP 04 - Seleção de Features", 
    expected_min_cols=len(features_importantes),
    previous_df=df_step03_features
)

print(f"\n📋 RESUMO STEP 04:")
print(f"   ✅ Features selecionadas: {df_step04_selected.shape[1]}")
print(f"   📊 Features removidas: {df_step03_features.shape[1] - df_step04_selected.shape[1]}")
print(f"   🎯 Shape final: {df_step04_selected.shape}")

# STEP 05: CORREÇÃO TEMPORAL - PENHASCO CRÍTICO!
print("\n📂 STEP 05: Correção temporal (PENHASCO CRÍTICO!)...")
print("🚨 ATENÇÃO: Esta é a área mais provável para perda massiva de features!")

# CHECKPOINT ANTES DA CORREÇÃO TEMPORAL
print(f"\n🔍 CHECKPOINT ANTES DA CORREÇÃO TEMPORAL:")
print(f"   Features disponíveis: {df_step04_selected.shape[1]}")
print(f"   Shape: {df_step04_selected.shape}")

# Criar cópia para correção temporal
df_step05_temporal = df_step04_selected.copy()

# ESTRATÉGIA CONSERVADORA: Aplicar shift apenas onde necessário
# Identificar features que precisam de shift (dados do presente/futuro)
features_to_shift = [
    'Abertura', 'Máxima', 'Mínima', 'Último', 'Volume',
    'MA_5', 'MA_10', 'MA_20', 'MA_50',
    'BB_Upper', 'BB_Lower', 'BB_Width', 'BB_Position',
    'RSI', 'MACD', 'Signal_Line',
    'atr_5', 'atr_10', 'atr_20',
    'volatility_5', 'volatility_10', 'volatility_20',
    'Price_Range', 'Price_Position', 'Gap', 'hl_close_ratio',
    'true_range', 'high_close_prev', 'low_close_prev',
    'returns', 'Variacao'
]

# Features que NÃO precisam de shift (dados temporais do passado)
features_no_shift = [
    'Data', 'day_of_week', 'month', 'quarter', 'year',
    'is_month_start', 'is_month_end', 'is_quarter_end',
    'Target'
]

# Verificar quais features estão disponíveis
available_to_shift = [f for f in features_to_shift if f in df_step05_temporal.columns]
available_no_shift = [f for f in features_no_shift if f in df_step05_temporal.columns]

print(f"\n📊 ANÁLISE DE FEATURES PARA SHIFT:")
print(f"   Features para shift: {len(available_to_shift)} de {len(features_to_shift)}")
print(f"   Features sem shift: {len(available_no_shift)} de {len(features_no_shift)}")

missing_to_shift = [f for f in features_to_shift if f not in df_step05_temporal.columns]
if missing_to_shift:
    print(f"   ⚠️ Features ausentes para shift: {missing_to_shift}")

# APLICAR SHIFT DE FORMA CONSERVADORA
print(f"\n⏰ Aplicando shift temporal...")

# Criar dataset final com features shifted
df_temporal_final = pd.DataFrame()

# 1. Manter features temporais sem shift
for feature in available_no_shift:
    df_temporal_final[feature] = df_step05_temporal[feature]

# 2. Aplicar shift nas features de preço/indicadores
for feature in available_to_shift:
    df_temporal_final[f'{feature}_shifted'] = df_step05_temporal[feature].shift(1)

# 3. Remover primeira linha (NaN devido ao shift)
df_temporal_final = df_temporal_final.iloc[1:].reset_index(drop=True)

# CHECKPOINT CRÍTICO APÓS CORREÇÃO TEMPORAL
print(f"\n🔍 CHECKPOINT APÓS CORREÇÃO TEMPORAL:")
print(f"   Features antes: {df_step05_temporal.shape[1]}")
print(f"   Features depois: {df_temporal_final.shape[1]}")
print(f"   Mudança: {df_temporal_final.shape[1] - df_step05_temporal.shape[1]:+d}")

# Verificar se temos as features shifted esperadas
expected_shifted = [f'{f}_shifted' for f in available_to_shift]
actual_shifted = [col for col in df_temporal_final.columns if col.endswith('_shifted')]

print(f"\n📊 VERIFICAÇÃO DE FEATURES SHIFTED:")
print(f"   Esperadas: {len(expected_shifted)}")
print(f"   Criadas: {len(actual_shifted)}")

missing_shifted = [f for f in expected_shifted if f not in df_temporal_final.columns]
if missing_shifted:
    print(f"   ❌ Features shifted ausentes: {missing_shifted[:5]}{'...' if len(missing_shifted) > 5 else ''}")
else:
    print(f"   ✅ Todas as features shifted criadas corretamente")

# Atualizar variável para próxima etapa
df_step05_temporal = df_temporal_final.copy()

# Auditoria da correção temporal
audit_step05 = audit_pipeline_step(
    df_step05_temporal, 
    "STEP 05 - Correção Temporal", 
    expected_min_cols=len(available_no_shift) + len(available_to_shift),
    previous_df=df_step04_selected
)

print(f"\n📋 RESUMO STEP 05:")
print(f"   ✅ Correção temporal aplicada")
print(f"   📊 Features shifted criadas: {len(actual_shifted)}")
print(f"   🎯 Shape final: {df_step05_temporal.shape}")

# STEP 06: PADRÕES DE CANDLESTICK
print("\n📂 STEP 06: Criando padrões de candlestick...")

# Criar cópia para candlestick
df_step06_candlestick = df_step05_temporal.copy()

# Verificar se temos as colunas OHLC shifted necessárias
ohlc_shifted_cols = ['Abertura_shifted', 'Máxima_shifted', 'Mínima_shifted', 'Último_shifted']
available_ohlc_shifted = [col for col in ohlc_shifted_cols if col in df_step06_candlestick.columns]

print(f"\n🕯️ VERIFICAÇÃO DE COLUNAS OHLC SHIFTED:")
print(f"   Necessárias: {ohlc_shifted_cols}")
print(f"   Disponíveis: {available_ohlc_shifted}")

if len(available_ohlc_shifted) == 4:
    print(f"   ✅ Todas as colunas OHLC shifted disponíveis")
    
    # Renomear temporariamente para facilitar cálculos
    ohlc_temp = df_step06_candlestick[available_ohlc_shifted].copy()
    ohlc_temp.columns = ['Open', 'High', 'Low', 'Close']
    
    # Funções de padrões de candlestick
    def detect_doji(df, threshold=0.1):
        body_size = abs(df['Close'] - df['Open'])
        total_range = df['High'] - df['Low']
        return (body_size / total_range < threshold).astype(int)
    
    def detect_hammer(df):
        body_size = abs(df['Close'] - df['Open'])
        lower_shadow = df[['Open', 'Close']].min(axis=1) - df['Low']
        upper_shadow = df['High'] - df[['Open', 'Close']].max(axis=1)
        return ((lower_shadow > 2 * body_size) & (upper_shadow < body_size)).astype(int)
    
    def detect_shooting_star(df):
        body_size = abs(df['Close'] - df['Open'])
        lower_shadow = df[['Open', 'Close']].min(axis=1) - df['Low']
        upper_shadow = df['High'] - df[['Open', 'Close']].max(axis=1)
        return ((upper_shadow > 2 * body_size) & (lower_shadow < body_size)).astype(int)
    
    def detect_engulfing_bullish(df):
        current_bullish = df['Close'] > df['Open']
        prev_bearish = (df['Close'].shift(1) < df['Open'].shift(1))
        current_open_below_prev_close = df['Open'] < df['Close'].shift(1)
        current_close_above_prev_open = df['Close'] > df['Open'].shift(1)
        return (current_bullish & prev_bearish & current_open_below_prev_close & current_close_above_prev_open).astype(int)
    
    # Aplicar padrões
    print(f"\n📊 Criando padrões de candlestick...")
    
    df_step06_candlestick['doji_prev'] = detect_doji(ohlc_temp)
    df_step06_candlestick['hammer_prev'] = detect_hammer(ohlc_temp)
    df_step06_candlestick['shooting_star_prev'] = detect_shooting_star(ohlc_temp)
    df_step06_candlestick['engulfing_bullish_prev'] = detect_engulfing_bullish(ohlc_temp)
    
    # Padrões adicionais
    df_step06_candlestick['bullish_candle_prev'] = (ohlc_temp['Close'] > ohlc_temp['Open']).astype(int)
    df_step06_candlestick['bearish_candle_prev'] = (ohlc_temp['Close'] < ohlc_temp['Open']).astype(int)
    
    # Métricas de candlestick
    df_step06_candlestick['body_size_prev'] = abs(ohlc_temp['Close'] - ohlc_temp['Open'])
    df_step06_candlestick['upper_shadow_prev'] = ohlc_temp['High'] - ohlc_temp[['Open', 'Close']].max(axis=1)
    df_step06_candlestick['lower_shadow_prev'] = ohlc_temp[['Open', 'Close']].min(axis=1) - ohlc_temp['Low']
    
    candlestick_features_created = 9
    print(f"   ✅ {candlestick_features_created} features de candlestick criadas")
    
else:
    print(f"   ❌ Colunas OHLC shifted insuficientes")
    print(f"   Ausentes: {[col for col in ohlc_shifted_cols if col not in df_step06_candlestick.columns]}")
    candlestick_features_created = 0

# Auditoria dos padrões de candlestick
audit_step06 = audit_pipeline_step(
    df_step06_candlestick, 
    "STEP 06 - Padrões de Candlestick", 
    expected_min_cols=df_step05_temporal.shape[1] + candlestick_features_created,
    previous_df=df_step05_temporal
)

print(f"\n📋 RESUMO STEP 06:")
print(f"   ✅ Features de candlestick criadas: {candlestick_features_created}")
print(f"   📊 Total de features: {df_step06_candlestick.shape[1]}")
print(f"   🎯 Shape final: {df_step06_candlestick.shape}")

# STEP 07: DATASET FINAL E AUDITORIA COMPLETA
print("\n📂 STEP 07: Preparando dataset final...")

# Criar dataset final
df_step07_final = df_step06_candlestick.copy()

# Limpeza final - remover linhas com muitos NaN
print(f"\n🧹 Limpeza final de dados...")
print(f"   Shape antes da limpeza: {df_step07_final.shape}")

# Contar NaN por linha
nan_counts = df_step07_final.isnull().sum(axis=1)
threshold = df_step07_final.shape[1] * 0.3  # Remover linhas com mais de 30% de NaN

df_step07_final = df_step07_final[nan_counts <= threshold]
print(f"   Shape após limpeza: {df_step07_final.shape}")
print(f"   Linhas removidas: {df_step06_candlestick.shape[0] - df_step07_final.shape[0]}")

# Auditoria final
audit_step07 = audit_pipeline_step(
    df_step07_final, 
    "STEP 07 - Dataset Final", 
    expected_min_cols=50,  # Esperamos pelo menos 50 features
    previous_df=df_step06_candlestick
)

print(f"\n📋 RESUMO STEP 07:")
print(f"   ✅ Dataset final preparado")
print(f"   📊 Features finais: {df_step07_final.shape[1]}")
print(f"   🎯 Shape final: {df_step07_final.shape}")
print(f"   💾 Pronto para modelagem!")

# Preparar splits temporais usando o dataset final
def create_temporal_split(df, test_size=0.2):
    df_sorted = df.sort_values('Data').reset_index(drop=True) if 'Data' in df.columns else df.copy()
    split_idx = int(len(df_sorted) * (1 - test_size))
    
    train_df = df_sorted.iloc[:split_idx]
    test_df = df_sorted.iloc[split_idx:]
    
    feature_cols = [col for col in df_sorted.columns if col not in ['Data', 'Target']]
    
    X_train = train_df[feature_cols]
    X_test = test_df[feature_cols]
    y_train = train_df['Target']
    y_test = test_df['Target']
    
    return X_train, X_test, y_train, y_test

# Usar o dataset final completo
X_train_final, X_test_final, y_train_final, y_test_final = create_temporal_split(df_step07_final)

# Remover NaN com forward fill
X_train_final = X_train_final.fillna(method='ffill').fillna(0)
X_test_final = X_test_final.fillna(method='ffill').fillna(0)

print(f"Dataset final - Train: {X_train_final.shape}, Test: {X_test_final.shape}")
print(f"NaN removidos com forward fill")

# Definir modelos
models = {
    'Logistic Regression': Pipeline([
        ('scaler', StandardScaler()),
        ('logreg', LogisticRegression(C=0.1, solver='liblinear', random_state=42))
    ]),
    
    'Random Forest': RandomForestClassifier(
        n_estimators=100, max_depth=10, min_samples_split=5,
        min_samples_leaf=2, random_state=42, n_jobs=-1
    ),
    
    'XGBoost': xgb.XGBClassifier(
        n_estimators=100, max_depth=6, learning_rate=0.1,
        subsample=0.8, colsample_bytree=0.8, random_state=42, eval_metric='logloss'
    ),
    
    'LightGBM': lgb.LGBMClassifier(
        n_estimators=100, max_depth=6, learning_rate=0.1,
        subsample=0.8, colsample_bytree=0.8, random_state=42, verbose=-1
    ),
    
    'SVM': Pipeline([
        ('scaler', StandardScaler()),
        ('svm', SVC(kernel='rbf', probability=True, random_state=42, C=1.0))
    ])
}

print(f"Modelos preparados: {list(models.keys())}")

# Treinar modelos com o dataset final completo
print("🚀 INICIANDO TREINAMENTO COM DATASET COMPLETO")
print(f"📊 Dataset: {X_train_final.shape} features")

trained_models_final = {}
training_times = {}
total_start_time = time.time()

for name, model in models.items():
    print(f"\nTreinando {name}...")
    start_time = time.time()
    
    # Criar nova instância do modelo
    if hasattr(model, 'get_params'):
        if isinstance(model, Pipeline):
            # Para pipelines, criar nova instância
            from sklearn.base import clone
            model_instance = clone(model)
        else:
            # Para modelos simples
            model_instance = model.__class__(**model.get_params())
    else:
        model_instance = model
    
    # Treinar modelo
    model_instance.fit(X_train_final, y_train_final)
    
    end_time = time.time()
    training_time = end_time - start_time
    training_times[name] = training_time
    
    trained_models_final[name] = model_instance
    print(f"   ✅ Concluído em {training_time:.2f}s")

total_time = time.time() - total_start_time

print(f"\n🎯 TREINAMENTO CONCLUÍDO!")
print(f"   ✅ {len(trained_models_final)} modelos treinados com sucesso")
print(f"   📊 Dataset com {X_train_final.shape[1]} features (incluindo candlestick)")
print(f"   ⏰ Tempo total: {total_time:.2f}s")

# Função de avaliação
def evaluate_model(model, X_train, X_test, y_train, y_test, model_name, dataset_name):
    start_time = time.time()
    
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
    
    accuracy = accuracy_score(y_test, y_pred)
    auc_score = roc_auc_score(y_test, y_pred_proba) if y_pred_proba is not None else None
    training_time = time.time() - start_time
    
    tscv = TimeSeriesSplit(n_splits=5)
    cv_scores = cross_val_score(model, X_train, y_train, cv=tscv, scoring='accuracy')
    
    return {
        'model_name': model_name,
        'dataset': dataset_name,
        'accuracy': accuracy,
        'auc_score': auc_score,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'training_time': training_time,
        'model': model
    }

print("Função de avaliação preparada")

# Executar olimpíada
results = []

for model_name, model in models.items():
    print(f"\nTestando {model_name}...")
    
    # Dataset A
    try:
        result_a = evaluate_model(model, X_train_a, X_test_a, y_train_a, y_test_a, 
                                model_name, "Dataset A")
        results.append(result_a)
        auc_str_a = f'{result_a["auc_score"]:.4f}' if result_a['auc_score'] else 'N/A'
        print(f'  Dataset A: Acc={result_a["accuracy"]:.4f}, AUC={auc_str_a}')

    except Exception as e:
        print(f"  Dataset A: Error - {e}")
    
    # Dataset B
    try:
        result_b = evaluate_model(model, X_train_b, X_test_b, y_train_b, y_test_b, 
                                model_name, "Dataset B")
        results.append(result_b)
        auc_str_b = f'{result_b["auc_score"]:.4f}' if result_b['auc_score'] else 'N/A'
        print(f'  Dataset B: Acc={result_b["accuracy"]:.4f}, AUC={auc_str_b}')

        
        if len(results) >= 2:
            diff = result_b['accuracy'] - result_a['accuracy']
            print(f"  Diferença: {diff:+.4f}")
    except Exception as e:
        print(f"  Dataset B: Error - {e}")

print(f"\nResultados coletados: {len(results)}")

# Avaliar modelos com dataset final completo
print("🎯 AVALIANDO MODELOS COM DATASET COMPLETO")
print(f"📊 Dataset de teste: {X_test_final.shape} features")

results_final = []
best_accuracy = 0
best_model_name = ""

for name, model in trained_models_final.items():
    print(f"\nAvaliando {name}...")
    
    # Fazer predições
    y_pred = model.predict(X_test_final)
    y_pred_proba = model.predict_proba(X_test_final)[:, 1] if hasattr(model, 'predict_proba') else None
    
    # Calcular métricas
    accuracy = accuracy_score(y_test_final, y_pred)
    auc_score = roc_auc_score(y_test_final, y_pred_proba) if y_pred_proba is not None else None
    
    # Cross-validation
    tscv = TimeSeriesSplit(n_splits=5)
    cv_scores = cross_val_score(model, X_train_final, y_train_final, cv=tscv, scoring='accuracy')
    
    result = {
        'model_name': name,
        'dataset': "Dataset Final Completo",
        'accuracy': accuracy,
        'auc_score': auc_score,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'model': model
    }
    
    results_final.append(result)
    
    # Acompanhar melhor modelo
    if accuracy > best_accuracy:
        best_accuracy = accuracy
        best_model_name = name
    
    print(f"   ✅ Accuracy: {accuracy:.4f} | AUC: {auc_score:.4f} | CV: {cv_scores.mean():.4f}±{cv_scores.std():.4f}")

print(f"\n🏆 RESULTADOS FINAIS:")
print(f"   🥇 Melhor modelo: {best_model_name} (Acc: {best_accuracy:.4f})")
print(f"   📊 Todos os modelos avaliados com dataset completo de {X_test_final.shape[1]} features")
print(f"   🕯️ Incluindo padrões de candlestick e features temporais")

# Análise dos resultados
if results:
    results_df = pd.DataFrame(results)
    
    print("\nTabela de Resultados:")
    print(f"{'Modelo':<20} {'Dataset':<12} {'Accuracy':<10} {'AUC':<8} {'CV Mean':<10} {'Tempo':<8}")
    print("-" * 75)
    
    for _, row in results_df.iterrows():
        auc_str = f"{row['auc_score']:.4f}" if row['auc_score'] is not None else "N/A"
        print(f"{row['model_name']:<20} {row['dataset']:<12} {row['accuracy']:<10.4f} {auc_str:<8} {row['cv_mean']:<10.4f} {row['training_time']:<8.2f}")
    
    # Ranking por dataset
    print("\nRanking Dataset A:")
    dataset_a_results = results_df[results_df['dataset'] == 'Dataset A'].sort_values('accuracy', ascending=False)
    for i, (_, row) in enumerate(dataset_a_results.iterrows(), 1):
        print(f"  {i}. {row['model_name']}: {row['accuracy']:.4f}")
    
    print("\nRanking Dataset B:")
    dataset_b_results = results_df[results_df['dataset'] == 'Dataset B'].sort_values('accuracy', ascending=False)
    for i, (_, row) in enumerate(dataset_b_results.iterrows(), 1):
        print(f"  {i}. {row['model_name']}: {row['accuracy']:.4f}")
    
    # Impacto das features de candlestick
    print("\nImpacto das Features de Candlestick:")
    candlestick_impact = []
    
    for model_name in results_df['model_name'].unique():
        model_results = results_df[results_df['model_name'] == model_name]
        if len(model_results) == 2:
            baseline = model_results[model_results['dataset'] == 'Dataset A'].iloc[0]
            enriched = model_results[model_results['dataset'] == 'Dataset B'].iloc[0]
            
            improvement = enriched['accuracy'] - baseline['accuracy']
            candlestick_impact.append(improvement)
            
            print(f"  {model_name}: {baseline['accuracy']:.4f} -> {enriched['accuracy']:.4f} ({improvement:+.4f})")
    
    if candlestick_impact:
        avg_improvement = np.mean(candlestick_impact)
        positive_count = sum(1 for x in candlestick_impact if x > 0)
        
        print(f"\nResumo do Impacto:")
        print(f"  Melhoria média: {avg_improvement:+.4f}")
        print(f"  Modelos que melhoraram: {positive_count}/{len(candlestick_impact)}")
        
        if avg_improvement > 0.01:
            print(f"  Conclusão: Features de candlestick agregam valor significativo")
        elif avg_improvement > 0:
            print(f"  Conclusão: Features de candlestick agregam valor marginal")
        else:
            print(f"  Conclusão: Features de candlestick não agregam valor")
    
    # Campeão geral
    best_result = results_df.loc[results_df['accuracy'].idxmax()]
    print(f"\nCampeão Geral:")
    print(f"  Modelo: {best_result['model_name']}")
    print(f"  Dataset: {best_result['dataset']}")
    print(f"  Accuracy: {best_result['accuracy']:.4f}")
    print(f"  AUC: {best_result['auc_score']:.4f}" if best_result['auc_score'] else "  AUC: N/A")

else:
    print("Nenhum resultado coletado")

# Ensemble dos melhores
if len(results) >= 6:  # Pelo menos 3 modelos testados em ambos datasets
    dataset_b_results = [r for r in results if r['dataset'] == 'Dataset B']
    
    if len(dataset_b_results) >= 3:
        dataset_b_results.sort(key=lambda x: x['accuracy'], reverse=True)
        top_3 = dataset_b_results[:3]
        
        print(f"\nTop 3 para ensemble: {[r['model_name'] for r in top_3]}")
        
        ensemble_estimators = []
        for i, result in enumerate(top_3):
            name = f"model_{i+1}"
            ensemble_estimators.append((name, result['model']))
        
        voting_classifier = VotingClassifier(estimators=ensemble_estimators, voting='soft')
        
        try:
            ensemble_result = evaluate_model(
                voting_classifier, X_train_b, X_test_b, y_train_b, y_test_b,
                "Voting Classifier", "Dataset B"
            )
            
            print(f"\nEnsemble Result:")
            print(f"  Accuracy: {ensemble_result['accuracy']:.4f}")
            print(f"  AUC: {ensemble_result['auc_score']:.4f}")
            
            best_individual = top_3[0]
            improvement = ensemble_result['accuracy'] - best_individual['accuracy']
            print(f"  vs Best Individual: {improvement:+.4f}")
            
            results.append(ensemble_result)
            
        except Exception as e:
            print(f"Ensemble error: {e}")

print("\nOlimpíada concluída")

# Visualização simples
if results:
    results_df = pd.DataFrame(results)
    
    plt.figure(figsize=(12, 8))
    
    # Gráfico de accuracy por modelo e dataset
    plt.subplot(2, 2, 1)
    models_list = results_df['model_name'].unique()
    datasets_list = results_df['dataset'].unique()
    
    x = np.arange(len(models_list))
    width = 0.35
    
    for i, dataset in enumerate(datasets_list):
        dataset_data = results_df[results_df['dataset'] == dataset]
        accuracies = []
        
        for model in models_list:
            model_data = dataset_data[dataset_data['model_name'] == model]
            if len(model_data) > 0:
                accuracies.append(model_data.iloc[0]['accuracy'])
            else:
                accuracies.append(0)
        
        plt.bar(x + i*width, accuracies, width, label=dataset, alpha=0.8)
    
    plt.xlabel('Modelos')
    plt.ylabel('Accuracy')
    plt.title('Accuracy por Modelo e Dataset')
    plt.xticks(x + width/2, models_list, rotation=45, ha='right')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Tempo vs Accuracy
    plt.subplot(2, 2, 2)
    plt.scatter([r['training_time'] for r in results], 
               [r['accuracy'] for r in results], alpha=0.7)
    plt.xlabel('Tempo de Treinamento (s)')
    plt.ylabel('Accuracy')
    plt.title('Accuracy vs Tempo')
    plt.grid(True, alpha=0.3)
    
    # Distribuição de AUC
    plt.subplot(2, 2, 3)
    auc_scores = [r['auc_score'] for r in results if r['auc_score'] is not None]
    if auc_scores:
        plt.hist(auc_scores, bins=10, alpha=0.7, edgecolor='black')
        plt.xlabel('AUC Score')
        plt.ylabel('Frequência')
        plt.title('Distribuição AUC')
        plt.grid(True, alpha=0.3)
    
    # Impacto candlestick
    plt.subplot(2, 2, 4)
    if 'candlestick_impact' in locals() and candlestick_impact:
        models_impact = [results_df[results_df['model_name'] == model]['model_name'].iloc[0] 
                        for model in results_df['model_name'].unique() 
                        if len(results_df[results_df['model_name'] == model]) == 2]
        
        if len(models_impact) == len(candlestick_impact):
            colors = ['green' if x > 0 else 'red' for x in candlestick_impact]
            plt.bar(models_impact, candlestick_impact, color=colors, alpha=0.7)
            plt.xlabel('Modelos')
            plt.ylabel('Melhoria Accuracy')
            plt.title('Impacto Features Candlestick')
            plt.xticks(rotation=45, ha='right')
            plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

print("Análise visual concluída")